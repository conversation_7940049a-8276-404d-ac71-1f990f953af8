
import streamlit as st
import streamlit_authenticator as stauth
import yaml
from yaml.loader import SafeLoader
import os


st.set_page_config(page_title="RAG application", layout="wide")


# --- Hide sidebar if not authenticated ---
def hide_sidebar():
    hide_sidebar_style = """
        <style>
        [data-testid="stSidebar"], .stSidebar, section[data-testid="stSidebar"] {
            display: none !important;
        }
        </style>
    """
    st.markdown(hide_sidebar_style, unsafe_allow_html=True)

# --- Logo (only show if authenticated) ---
def show_logo():
    st.logo(
        "static/images/tiger-analytics.png",
        size="large",
        icon_image="static/images/tiger-analytics.png",
    )
    logo_style = """
        <style>
            .stLogo {height: 3rem !important}
        </style>
    """
    st.markdown(logo_style, unsafe_allow_html=True)


# hide header
hide_streamlit_header = """<style>
                           header {display: none !important;}
                           </style>
                        """
st.markdown(hide_streamlit_header, unsafe_allow_html=True)


stMainBlockContainerPaddingTop = """
    <style>
        .stMainBlockContainer {
            padding-top: 1rem !important;
        }
    </style>
    """
st.markdown(stMainBlockContainerPaddingTop, unsafe_allow_html=True)

CONFIG_FILE = 'config/login_config.yaml'

# Create config directory if it doesn't exist
os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)

# Load configuration
if os.path.exists(CONFIG_FILE):
    with open(CONFIG_FILE) as file:
        config = yaml.load(file, Loader=SafeLoader)
else:
    config = {
        'credentials': {'usernames': {}},
        'cookie': {
            'expiry_days': 30,
            'key': 'some_signature_key',
            'name': 'some_cookie_name'
        }
    }

# Initialize authenticator
authenticator = stauth.Authenticate(
    config['credentials'],
    config['cookie']['name'],
    config['cookie']['key'],
    config['cookie']['expiry_days']
)

# Initialize session state variables if they don't exist
if 'authentication_status' not in st.session_state:
    st.session_state['authentication_status'] = None
if 'show_register' not in st.session_state:
    st.session_state['show_register'] = False
if 'username' not in st.session_state:
    st.session_state['username'] = None
if 'name' not in st.session_state:
    st.session_state['name'] = None


# --- Main App Logic ---
if st.session_state['show_register']:
    # Registration Page
    hide_sidebar()
    st.title("Register")
    back_button = st.button("Back to Login")
    if back_button:
        st.session_state['show_register'] = False
        st.rerun()
    try:
        registration_result = authenticator.register_user(location='main')
        if registration_result is not None:
            email, username, name = registration_result
            if email:
                st.success('User registered successfully')
                # Save updated config
                with open(CONFIG_FILE, 'w') as file:
                    yaml.dump(config, file, default_flow_style=False)
                # Automatically switch back to login view
                st.session_state['show_register'] = False
                st.rerun()
    except Exception as e:
        st.error(e)
else:
    # Login Page or Main Content based on authentication status
    authenticator.login(location='main')

    if st.session_state.get('authentication_status'):
        # User is authenticated - show main content
        show_logo()
        nav_item_size = """
            <style>
                ul > li > div > a > span {font-size: 1.5rem !important}
            </style>
        """
        st.markdown(nav_item_size, unsafe_allow_html=True)
        # pages
        pg = st.navigation(
            [st.Page("chat.py", title="Chat"),
             st.Page("settings.py", title="Settings"),
             st.Page("langfuse_monitor.py", title="Dashboard")],
            position="sidebar",
        )
        pg.run()

        # Add user info and logout to sidebar
        with st.sidebar:
            # Display logged-in user information
            st.markdown("---")
            user_info_style = """
                <style>
                    .user-info {
                        background-color: #f0f2f6;
                        padding: 10px;
                        border-radius: 5px;
                        margin-bottom: 10px;
                    }
                    .user-info h4 {
                        margin: 0;
                        color: #262730;
                    }
                    .user-info p {
                        margin: 5px 0 0 0;
                        color: #666;
                        font-size: 14px;
                    }
                </style>
            """
            st.markdown(user_info_style, unsafe_allow_html=True)

            user_name = st.session_state.get("username", "User")
            name = st.session_state.get("name", "User")

            st.markdown(f"""
                <div class="user-info">
                    <h4>👤 Logged in as:</h4>
                    <p><strong>{name}</strong></p>
                    <p>@{user_name}</p>
                </div>
            """, unsafe_allow_html=True)

            st.markdown("---")
            authenticator.logout('Logout', 'main')

        # footer
        footer_element = """
            <h3 class='footer-element'>Powered by Tiger Analytics&trade;</h3>
        """
        footer_element_style = """
            <style>
                .footer-element {
                        position: fixed;
                        bottom: 0px !important;
                        padding: 1rem;
                    }
            </style>
        """
        st.sidebar.markdown(footer_element, unsafe_allow_html=True)
        st.sidebar.markdown(footer_element_style, unsafe_allow_html=True)
    else:
        # Not authenticated (either failed or not attempted)
        hide_sidebar()
        if st.session_state.get('authentication_status') is False:
            st.error('Username/password is incorrect')
        elif st.session_state.get('authentication_status') is None:
            st.warning('Please enter your username and password')
        st.write("Don't have an account?")
        register_button = st.button("Register")
        if register_button:
            st.session_state['show_register'] = True
            st.rerun()

# Force rerun after logout to clear sidebar
if (
    'authentication_status' in st.session_state
    and st.session_state['authentication_status'] is None
    and st.session_state.get('just_logged_out') != True
):
    st.session_state['just_logged_out'] = True
    st.rerun()
else:
    st.session_state['just_logged_out'] = False