
import streamlit as st
import yaml

def main():
    st.title("ACEN Monitoring Dashboard 📊")

    # Get user information from session state
    user_name = st.session_state.get("username", "User")
    name = st.session_state.get("name", "User")

    st.write(f"Welcome to the Dashboard, {name}!")

    st.write(
        """
        Click the button below to open your Langfuse project dashboard in a new tab.
        Here you can monitor, debug, and evaluate your LLM application's traces,
        generations, costs, and performance.
        """
    )

    try:
        #Load config
        with open('config/config.yaml', "r") as f:
            config = yaml.safe_load(f)

        LANGFUSE_HOST = config['llm']['langfuse_host']
        LANGFUSE_PROJECT_ID = config['llm']['langfuse_project_id']

        langfuse_dashboard_url = f"{LANGFUSE_HOST}/project/{LANGFUSE_PROJECT_ID}"

        # Use st.link_button to create a clickable button that redirects
        st.link_button(
            label="Go to ACEN Dashboard",
            url=langfuse_dashboard_url,
            help="Opens the ACEN Monitoring  dashboard in a new browser tab.",
            type="primary"
        )

        st.info(
            "Make sure you are logged into your Langfuse account in your browser to view the dashboard."
        )
    except Exception as e:
        st.error(f"Error loading dashboard configuration: {e}")
        st.info("Please check your configuration file.")

# Run the main function
if __name__ == "__main__":
    main()
else:
    main()  # Also run when imported as a page