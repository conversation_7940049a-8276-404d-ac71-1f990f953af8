"""
Assistant module for handling API interactions with language models.

This module provides a class for managing API calls to language models
with proper error handling and response formatting.
"""

import os
import json
import logging
from typing import Optional, Literal, Dict, Type, Any, Union, cast, List

from openai import OpenAI, AzureOpenAI
from openai.types.chat import ChatCompletion
from dotenv import load_dotenv
from pydantic import BaseModel, Field, ValidationError

# Configure logging
logger = logging.getLogger(__name__)

class AssistantError(Exception):
    """Base exception for assistant errors"""
    pass

class APIError(AssistantError):
    """Raised when API calls fail"""
    pass

class ValidationError(AssistantError):
    """Raised when validation fails"""
    pass

class AssistantConfig(BaseModel):
    """Configuration model for Assistant class."""
    
    api_key: str = Field(..., description="API key for authentication")
    base_url: str = Field(..., description="Base URL for API endpoint")
    model: str = Field(..., description="Default model to use")
    api_version: str = Field(..., description="API version to use for Azure OpenAI")

class AssistantResponse(BaseModel):
    """Standardized response model for assistant outputs."""
    
    content: str = Field(..., description="Generated content")
    model: str = Field(..., description="Model used for generation")
    usage: Dict[str, int] = Field(..., description="Token usage statistics")

class Assistant:
    """
    A class to handle interactions with language model APIs.
    
    Attributes:
        client (OpenAI): OpenAI client instance
        model (str): Default model identifier
    """

    def __init__(self) -> None:
        """Initialize the Assistant with configuration from environment variables."""
        try:
            load_dotenv()
            
            config = AssistantConfig(
                api_key=os.getenv("OPENAI_API_KEY", ""),
                base_url=os.getenv("OPENAI_BASE_URL", ""),
                model=os.getenv("OPENAI_MODEL_NAME", ""),
                api_version=os.getenv("OPENAI_API_VERSION", "")
            )
            if "azure" in config.base_url.lower():
                self.client = AzureOpenAI(
                    api_key=config.api_key,
                    api_version=config.api_version,
                    azure_endpoint=config.base_url
                )
            else:
                self.client = OpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url
                )
            self.model = config.model
            
            logger.info("Assistant initialized successfully")
            
        except ValidationError as e:
            logger.error("Configuration validation failed: %s", e)
            raise
        except Exception as e:
            logger.error("Failed to initialize Assistant: %s", e)
            raise

    def _validate_temperature(self, temperature: float) -> None:
        """
        Validate temperature parameter.
        
        Args:
            temperature (float): Temperature value to validate
            
        Raises:
            ValueError: If temperature is not between 0 and 1
        """
        if not 0 <= temperature <= 1:
            logger.error("Invalid temperature value: %f", temperature)
            raise ValueError("Temperature must be between 0 and 1")

    def _prepare_messages(
        self,
        prompt: str,
        system_message: Optional[str],
        output_json: Optional[Literal["json"]]
    ) -> list[Dict[str, str]]:
        """
        Prepare message list for API call.
        
        Args:
            prompt (str): User prompt
            system_message (Optional[str]): System message
            output_json (Optional[Literal["json"]]): JSON output flag
            
        Returns:
            list[Dict[str, str]]: Prepared messages
        """
        messages = []
        
        if system_message:
            messages.append({"role": "system", "content": system_message})
        elif output_json == "json":
            messages.append({
                "role": "system",
                "content": "You are a helpful assistant designed to output JSON."
            })
        
        messages.append({"role": "user", "content": prompt})
        return messages

    def _prepare_api_parameters(
        self,
        messages: list[Dict[str, str]],
        model: str,
        temperature: float,
        output_format: Optional[Union[Literal["json"], Type[BaseModel]]]
    ) -> Dict[str, Any]:
        """
        Prepare parameters for API call.
        
        Args:
            messages (list[Dict[str, str]]): Prepared messages
            model (str): Model identifier
            temperature (float): Temperature value
            output_format (Optional[Union[Literal["json"], Type[BaseModel]]]): Output format
            
        Returns:
            Dict[str, Any]: API parameters
        """
        params = {
            "model": model or self.model,
            "messages": messages,
            "temperature": temperature,
        }

        if output_format:
            params["response_format"] = (
                {"type": "json_object"}
                if output_format == "json"
                else output_format
            )
        
        return params

    def run(
        self,
        prompt: str,
        model: Optional[str] = None,
        output_json: Optional[Literal["json"]] = None,
        output_pydantic: Optional[Type[BaseModel]] = None,
        temperature: float = 0,
        system_message: Optional[str] = None
    ) -> AssistantResponse:
        """
        Generate a response using the language model API.

        Args:
            prompt (str): The user prompt/question
            model (Optional[str]): Model to use. Defaults to None.
            output_json (Optional[Literal["json"]]): JSON output flag. Defaults to None.
            output_pydantic (Optional[Type[BaseModel]]): Pydantic model. Defaults to None.
            temperature (float): Temperature for randomness. Defaults to 0.
            system_message (Optional[str]): System message. Defaults to None.

        Returns:
            AssistantResponse: Standardized response object

        Raises:
            ValueError: If temperature is invalid
            Exception: For API-related errors
        """
        try:
            logger.debug("Generating response for prompt: %s", prompt)
            
            self._validate_temperature(temperature)
            
            messages = self._prepare_messages(prompt, system_message, output_json)
            output_format = output_json or output_pydantic
            
            params = self._prepare_api_parameters(
                messages, model, temperature, output_format
            )
            
            response = self.client.beta.chat.completions.parse(**params)
            response = cast(ChatCompletion, response)
            
            result = AssistantResponse(
                content=response.choices[0].message.content,
                model=response.model,
                usage={
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens
                }
            )
            
            logger.debug("Successfully generated response")
            return result
            
        except ValueError as e:
            logger.error("Validation error: %s", e)
            raise ValidationError(str(e))
        except Exception as e:
            logger.error("API call failed: %s", e)
            raise APIError(f"Failed to generate response: {str(e)}")

    def get_embeddings(
        self,
        texts: List[str],
        model: str = "text-embedding-ada-002"
    ) -> List[List[float]]:
        """
        Generate embeddings for a list of texts.
        """
        try :
            response = self.client.embeddings.create(
                input=texts,
                model=model
            )
            # response.data is a list of dicts with 'embedding' key
            return [item.embedding for item in response.data]
        except Exception as e:
            raise APIError(f"Failed to geenrate embeddings: {e}")
        
