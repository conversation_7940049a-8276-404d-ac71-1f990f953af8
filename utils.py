import streamlit as st
import re

def scroll_to_bottom():
    scroll_script = """
    <script>
        window.scrollTo(0, document.body.scrollHeight);
    </script>
    """
    st.components.v1.html(scroll_script)


def clean_and_format_citations(raw_text):
    # Step 1: Normalize spacing, hyphenation, line breaks
    text = re.sub(r'\s*-\s*', '-', raw_text)             # Clean up broken hyphens
    text = re.sub(r'\s+', ' ', text)                     # Normalize whitespace
    text = text.strip()

    # Step 2: Normalize bullet point spacing
    text = text.replace("•", "\n• ")                     # Ensure bullets start on a new line
    text = re.sub(r'(?<=\d)\.\s+', r'. ', text)          # Fix numbered list spacing

    # Step 3: Split text into chunks — numbered (e.g., 7. ...) or bulleted (• ...)
    citation_pattern = r'(?=(?:\d{1,2}\.\s|•\s))'
    chunks = re.split(citation_pattern, text)

    # Step 4: Clean and format each chunk
    formatted = []
    for chunk in chunks:
        chunk = chunk.strip()
        if not chunk:
            continue
        chunk = chunk[0].upper() + chunk[1:] if len(chunk) > 1 else chunk
        formatted.append(chunk)

    # Step 5: Join all cleaned chunks
    return '\n\n'.join(formatted)

def expand_abbreviations(text):
    abbr_map = {
        "MBL": "Maximum Benefit Limit",
        "ABL": "Annual Benefit Limit",
    }
    # Regex pattern: match the abbreviations as full words (word boundaries)
    pattern = re.compile(r'\b(' + '|'.join(abbr_map.keys()) + r')\b')
    def replacer(match):
        abbr = match.group(0)
        return abbr_map[abbr]
    return pattern.sub(replacer, text)