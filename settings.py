import streamlit as st

def main():
    st.title("Settings")

    # Get user information from session state
    user_name = st.session_state.get("username", "User")
    name = st.session_state.get("name", "User")

    st.write(f"Welcome to the Settings page, {name}!")

    st.subheader("User Information")
    st.write(f"**Username:** {user_name}")
    st.write(f"**Display Name:** {name}")

    st.subheader("Application Settings")
    st.info("Settings functionality will be implemented here.")

    # Placeholder for future settings
    st.checkbox("Enable notifications", value=True)
    st.selectbox("Theme", ["Light", "Dark", "Auto"])
    st.slider("Chat history limit", min_value=10, max_value=100, value=50)

# Run the main function
if __name__ == "__main__":
    main()
else:
    main()  # Also run when imported as a page
