import ast
import datetime
import os
import yaml
import chromadb
import pandas as pd
from langchain.embeddings import AzureOpenAIEmbeddings
# from openai import AzureOpenAI
from langfuse.openai import AzureOpenAI
from sqlalchemy import Column, DateTime, Integer, Text, create_engine
from sqlalchemy.orm import declarative_base, sessionmaker

from models import (ChatbotResponse, ImageCitation, ImagePathsCitation,
                    PdfCitation, TextCitation, VideoCitation)

import warnings
warnings.filterwarnings("ignore")

#Load config
with open('config/config.yaml', "r") as f:
    config = yaml.safe_load(f)

os.environ['AZURE_OPENAI_API_KEY'] = config['llm']['openai_api_key_env_var']
os.environ['AZURE_OPENAI_ENDPOINT'] = config['llm']['openai_endpoint_env_var']
os.environ['AZURE_OPENAI_EMBEDDING_DEPLOYMENT'] = config['embeddings']['embedding_model']
os.environ["LANGFUSE_PUBLIC_KEY"] = config['llm']['langfuse_public_key']
os.environ["LANGFUSE_SECRET_KEY"] = config['llm']['langfuse_secret_key']
os.environ["HOST"] = config['llm']['langfuse_host']
# -----------------------------------------------------------------------------
# Environment & DB setup
# -----------------------------------------------------------------------------
# Azure OpenAI credentials via env vars
AZURE_API_KEY = os.getenv("AZURE_OPENAI_API_KEY")
AZURE_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
AZURE_API_VERSION = os.getenv("OPENAI_API_VERSION", "2024-07-01-preview")
TEMPERATURE = float(os.getenv("OPENAI_TEMPERATURE", "0"))

# Database URL via env var or default to local sqlite
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///chat_history.db")

Base = declarative_base()
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(bind=engine)
Base.metadata.create_all(engine)

class ChatRecord(Base):
    __tablename__ = "chat_records"
    id = Column(Integer, primary_key=True)
    user_query = Column(Text, nullable=False)
    assistant_response = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)


def save_user_query(session, query: str) -> int:
    rec = ChatRecord(user_query=query)
    session.add(rec)
    session.commit()
    return rec.id


def save_assistant_response(session, record_id: int, response: str):
    rec = session.query(ChatRecord).get(record_id)
    rec.assistant_response = response
    session.commit()


# -----------------------------------------------------------------------------
# User Session Mocking
# -----------------------------------------------------------------------------
# Mock user data
user_data = {
    'id': ['1234567890', '0987654321', '1111111111', '2222222222'],
    'username': ['ravi', 'tharun', 'sahil', 'adhithia'],
    'name': ['Ravi', 'Tharun', 'Sahil', 'Adhithia'],
    'role': ['user', 'user', 'admin', 'user'],
    'designation': ['staff', 'associate', 'AVP', 'Sr.associate'],
    'class': [1, 1, 3, 2],
    'company_id': ['ACEN1234', 'ACEN1234', 'ABC1234', 'AHP1234']
}
users_df = pd.DataFrame(user_data)

def get_user_info(user_name: str):
    user_row = users_df[users_df['username'].str.lower() == user_name.lower()]
    if user_row.empty:
        return None
    return user_row.iloc[0].to_dict()


# -----------------------------------------------------------------------------
# RAG helpers (ChromaDB)
# -----------------------------------------------------------------------------
def load_chroma_db(collection_name: str,
                   persist_directory: str = "rag_store") -> chromadb.api.models.Collection:
    try:
        client = chromadb.PersistentClient(path=persist_directory)
        return client.get_or_create_collection(name=collection_name)
    except Exception as e:
        raise RuntimeError(f"Error loading ChromaDB: {e}")


def query_top_chunks(query_text: str,
                     collection_name: str,
                     persist_directory: str = "rag_store",
                     top_k: int = 5,
                     user_info: dict = None) -> dict:
    """
    user_info: dict with keys ['id', 'name', 'role', 'designation', 'class', 'company_id']
    """
    collection = load_chroma_db(collection_name, persist_directory)
    embedder = AzureOpenAIEmbeddings(
        openai_api_key=AZURE_API_KEY,
        openai_api_version=AZURE_API_VERSION,
        azure_endpoint=AZURE_ENDPOINT,
        model=os.getenv("EMBEDDING_MODEL_NAME", "text-embedding-3-small"),
        chunk_size=int(os.getenv("EMBEDDING_CHUNK_SIZE", "1000"))
    )

    q_emb = embedder.embed_documents([query_text])

    # Add company_id filter if user is not admin
    where = None
    if user_info is not None and user_info.get('role', 'user') != 'admin':
        where = {"company_id": user_info.get('company_id')}

    query_args = {
        "query_embeddings": q_emb,
        "n_results": top_k
    }
    if where:
        query_args["where"] = where

    results = collection.query(**query_args)

    cleaned_results = {}
    documents = []
    metadatas = []
    for idx, doc in enumerate(results["documents"][0]):
        if doc not in documents:
            documents.append(doc)
            metadatas.append(results['metadatas'][0][idx])

    cleaned_results['documents'] = [documents[:top_k]]
    cleaned_results['metadatas'] = [metadatas[:top_k]]
    return cleaned_results

# -----------------------------------------------------------------------------
# Core RAG Streamer
# -----------------------------------------------------------------------------
class Document:
    def __init__(self, page_content: str, metadata: dict):
        self.page_content = page_content
        self.metadata = metadata

class Streamer:
    def __init__(self):
        self.client = AzureOpenAI(
            azure_endpoint=AZURE_ENDPOINT,
            api_key=AZURE_API_KEY,
            api_version=AZURE_API_VERSION
        )
        self.model = "gpt-4o-mini"
        self.persist_directory = "rag_store"
        self.collection_name = "acen_docs" #"kearney_32"
        self.response_parts: list[str] = []

    def generate_stream(self, user_query: str, user_name: str, top_k: int = 5):
        user_info = get_user_info(user_name)
        if user_info is None:
            user_details = ""
        else:
            user_details = f"User Designation: {user_info.get('designation', '')}, User Class: {user_info.get('class', '')}"
        raw = query_top_chunks(
            query_text=user_query,
            collection_name=self.collection_name,
            persist_directory=self.persist_directory,
            top_k=top_k,
            user_info=user_info
        )
        contexts = raw["documents"]

        prompt = f"""
        You are an expert corporate benefits assistant. Your primary goal is to answer employee questions with high accuracy and security, based ONLY on the official documents provided as context.

        ## PRIME DIRECTIVE: Be Comprehensive but Precise
        - If a user's question is **ambiguous** and could apply to multiple categories (e.g., just "What is my MBL?"), you MUST provide the answer for ALL relevant categories found in the context.
        - However, if the user's question is **specific** and mentions a context (e.g., "What is my MBL for the *basic medical* plan?"), you MUST ONLY provide the answer for that specific context.

        ## Key Definitions
        - **MBL**: Stands for "Maximum Benefit Limit".
        - **ABL**: Stands for "Annual Benefit Limit".

        ## Your Instructions and Boundaries
        1.  **Base All Answers on Context:** You must ONLY use the information within the provided 'Context' section. Do not use external knowledge.
        2.  **Handle Missing Information:** If the answer to the user's specific question cannot be found in the context, you must clearly state that and mention the topic you searched for. **Do not use a generic phrase.** Instead, incorporate the user's query into your response and then direct them to HR.
        3.  **Handle General Conversation:** If the user provides a greeting or engages in conversation not related to benefits, respond politely and guide them to ask a question about their benefits.
        4.  **Deny Sensitive Data Requests:** NEVER provide information about sensitive data like payslips. Direct the user to the secure HR portal.

        ## Table Analysis and Step-by-Step Thinking
        You must follow these steps before giving an answer:
        1. **Apply Hierarchical Context Rule:** When you see a list with a main point and indented sub-points (e.g., a main item "1." followed by sub-items "a.", "b.", "c."), any value or coverage detail mentioned for the main point **also applies to all of its sub-points**, unless a sub-point explicitly states its own different value.
        2.  **Analyze the Query:** First, determine if the user's query is general or specific.
        3.  **Scan the Entire Context:** Find all tables or sections relevant to the query.
        4.  **Think Step-by-Step:**
            * **Step 1:** Identify key terms in the question and the user's details.
            * **Step 2:** Locate all matching rows/sections in the context.
            * **Step 3:** Extract all relevant values, respecting whether the query was general or specific.
            * **Step 4:** Formulate a comprehensive but targeted answer based on your findings.

        ## Answer Examples (For Formatting and Logic Only)
        **IMPORTANT:** The examples below are to show you the desired format and reasoning process. The specific values and details in these examples are illustrative only. **You must derive your final answer exclusively from the 'Context' provided in the 'Final Task' section below.**

        ---
        **Example 1: Ambiguous Question**
        - **Question:** "What is my MBL for Class 2?"
        - **Answer:** "Based on the documents, the Maximum Benefit Limit (MBL) for a Class 2 employee applies in several contexts:
        - Under **Basic Medical - Employees**: The MBL is ₱[example value].
        - Under **Dreaded Disease - Employees**: The MBL is ₱[example value]."

        ---
        **Example 2: Specific Question**
        - **Question:** "How much is the surgeon's fee for an AVP under the Basic Medical plan for employees?"
        - **Answer:** "Under the Basic Medical - Employees plan, the Surgeon's Fee for an AVP (Class 3) is ₱[example value]."

        ---
        **Example 3: Information Not Found**
        - **Question:** "What is the policy for dental implants?"
        - **Answer:** "I do not have the answer to that question in the provided documents. Please contact your HR Business Partner for further guidance."

        ---
        **Example 4: Sensitive Data Request**
        - **Question:** "Can you show me my last payslip?"
        - **Answer:** "For your security, I cannot access sensitive personal information like payslips. Please use the official and secure HR portal for this data."

        ---
        **Example 5: General Greeting**
        - **Question:** "Hi there"
        - **Answer:** "Hello! I'm here to help you with questions about your benefits. What would you like to know?"

        ---
        ## Final Task

        Based on all the rules and examples above, please answer the following question using the provided context and user details.

        **Context:**
        {contexts}

        **User Details:**
        {user_details}

        **Question:**
        {user_query}

        **Answer:**
        """
        for update in self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            stream=True,
            temperature=0.0
        ):
            if not getattr(update, "choices", None) or len(update.choices) == 0:
                continue
            chunk = getattr(update.choices[0].delta, "content", "") or ""
            if chunk:
                self.response_parts.append(chunk)
                yield chunk

    def generate_answer(self, user_name: str) -> ChatbotResponse:
        answer = "".join(self.response_parts)
        session = SessionLocal()
        citations = []
        user_info = get_user_info(user_name)
        raw = query_top_chunks(
            query_text=answer,
            collection_name=self.collection_name,
            persist_directory=self.persist_directory,
            user_info=user_info
        )
        docs      = raw["documents"][0]
        metas     = raw["metadatas"][0]

        for text, meta in zip(docs, metas):
            filename = meta.get("source_file", "").replace(".pdf", "")
            pages = [] 
            if True:
                page_number = meta.get("page_number", "[]")
                try:
                    if type(page_number)==str:
                        page_number = ast.literal_eval(page_number)
                    if type(page_number)==int:
                        pages.append(page_number)
                    if type(page_number)==list:
                        pages+=page_number
                    image_list = [f'./page_images/{filename}_page_{i}.png' for i in pages]
                except (ValueError, SyntaxError):
                    image_list = []
                common_kwargs = {
                    "document_name": meta.get("source_file", ""),
                    'page_number': ', '.join([str(i) for i in pages]),
                    "content": '',
                    "source_url": meta.get("uris"),
                    "similarity_score": 1.0 ,
                }
                if image_list:
                    citations.append(
                        ImagePathsCitation(
                            modality="imagelist",
                            **common_kwargs,
                            image_paths=[p for p in image_list]
                        )
                    )
        session.close()
        return ChatbotResponse(answer=answer, citations=citations)

# -----------------------------------------------------------------------------
# Simple non-streaming RAG
# -----------------------------------------------------------------------------
def generate_response(query: str, user_name: str, top_k: int = 5) -> ChatbotResponse:
    session = SessionLocal()
    record_id = save_user_query(session, query)
    user_info = get_user_info(user_name)
    raw = query_top_chunks(
        query_text=query,
        collection_name="acen_docs",
        persist_directory="rag_store",
        top_k=top_k,
        user_info=user_info
    )
    docs = [Document(text, meta) for text, meta in zip(raw["documents"][0], raw["metadatas"][0])]
    contexts = raw["documents"]
    user_details = f"User Designation: {user_info.get('designation', '')}, User Class: {user_info.get('class', '')}"
    prompt = ("answer the question based on the context provided\n"
    f"question: {query}\n"
    f"contexts: {contexts}\n"
    f"{user_details}"
    )
    azure_ai = AzureOpenAI(
        azure_endpoint=AZURE_ENDPOINT,
        api_key=AZURE_API_KEY,
        api_version=AZURE_API_VERSION
    )
    answer = azure_ai.chat.completions.create(
        model=os.getenv("CHAT_MODEL_NAME", "gpt-4o-mini"),
        messages=[{"role": "user", "content": prompt}]
    ).choices[0].message.content
    citations = [
        TextCitation(
            modality='text',
            document_name='Trade Pricing and Promotion Optimization.txt',
            content=""" may drive the sales from client's perspective?
* What is the list of predefined filters (business rules) used by the clients?

  .. admonition:: Example
    :class: exampleadmonition

    XYZ provides promotion calendar only for products with more than x sales or y distribution.

* What is the time window to consider for reaction time? ::

    If the promotion calendar is provided at any given time,
    the client needs at least n number of days/weeks/months to execute the promotion.

  .. admonition:: Example
    :class: exampleadmonition

    XYZ wanted a 1 month reaction time, so the m""",
            source_url='http://localhost:8000/Trade%20Pricing%20and%20Promotion%20Optimization.txt',
            similarity_score=0.2871220838569282
        ),
        TextCitation(
            modality='text',
            document_name='Trade Pricing and Promotion Optimization.txt',
            content="""XYZ wanted a 1 month reaction time, so the model was designed to provide promotion calendar a month ahead.

Trade Spend Allocation
======================

.. _trade_spend_allocation:

.. figure:: ../images/tpo/trade_spend_allocation.png
  :align: center
  :height: 200px
  :alt: trade_spend_allocation

Sample trade spend allocation flow

In order to understand the complete process, :numref:trade_spend_allocation represents a sample trade spend allocation flow from the time of fund allocation at the organization level to execution at product level. :highlighttext:This is helpful in """,
            source_url='http://localhost:8000/Trade%20Pricing%20and%20Promotion%20Optimization.txt',
            similarity_score=0.2586339683041994
        )
    ]
    # append retrieval citations
    for text, meta in zip(raw["documents"][0], raw["metadatas"][0]):
        citations.append(TextCitation(
            modality="text",
            document_name=meta.get("source"),
            content=text,
            source_url=meta.get("source_url"),
            similarity_score=meta.get("score", 0.0)
        ))
    save_assistant_response(session, record_id, answer)
    session.close()
    return ChatbotResponse(answer=answer, citations=citations)
