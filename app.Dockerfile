FROM python:3.9-slim

WORKDIR /app

# Copy only the requirements file first to leverage Docker layer caching
COPY kearney_requirements.txt /app/

# Install dependencies before copying the full project
RUN pip install --upgrade pip && pip install --no-cache-dir -r kearney_requirements.txt


# COPY data /app/data
# COPY ./.env /app/.env


EXPOSE 8502
COPY . /app
CMD ["streamlit", "run", "index.py", "--server.port", "8502"]