from typing import List, Literal, Optional, Union

from pydantic import BaseModel, HttpUrl


class CitationBase(BaseModel):
    modality: Literal["pdf", "video", "text", "image", "html", "imagelist"]
    document_name: str
    content: str
    source_url: Optional[HttpUrl]
    similarity_score: float


class PdfCitation(CitationBase):
    modality: Literal["pdf"]
    page_number: Optional[int]


class VideoCitation(CitationBase):
    modality: Literal["video"]
    timestamp: Optional[str]


class TextCitation(CitationBase):
    modality: Literal["text"]


class ImageCitation(CitationBase):
    modality: Literal["image"]
    bounding_box: Optional[List[int]]

class ImagePathsCitation(CitationBase):
    modality: Literal["imagelist"]
    image_paths: List[str] 

Citation = Union[
    PdfCitation,
    VideoCitation,
    TextCitation,
    ImageCitation,
    ImagePathsCitation,
]


class ChatbotResponse(BaseModel):
    answer: str
    citations: List[Citation]
