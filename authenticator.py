import streamlit as st
import streamlit_authenticator as stauth
import yaml
from yaml.loader import <PERSON>Loader
import os

# Initialize session state variables if they don't exist
if 'authentication_status' not in st.session_state:
    st.session_state['authentication_status'] = None
if 'show_register' not in st.session_state:
    st.session_state['show_register'] = False
if 'username' not in st.session_state:
    st.session_state['username'] = None
if 'name' not in st.session_state:
    st.session_state['name'] = None

CONFIG_FILE = 'config/login_config.yaml'

# Create config directory if it doesn't exist
os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)

# Load configuration
if os.path.exists(CONFIG_FILE):
    with open(CONFIG_FILE) as file:
        config = yaml.load(file, Loader=SafeLoader)
else:
    config = {
        'credentials': {'usernames': {}},
        'cookie': {
            'expiry_days': 30,
            'key': 'some_signature_key',
            'name': 'some_cookie_name'
        }
    }

# Initialize authenticator
authenticator = stauth.Authenticate(
    config['credentials'],
    config['cookie']['name'],
    config['cookie']['key'],
    config['cookie']['expiry_days']
)

def toggle_register_form():
    st.session_state['show_register'] = not st.session_state['show_register']

# Check if we need to perform registration
if st.session_state['show_register']:
    # Registration Page
    st.title("Register")
    back_button = st.button("Back to Login")
    if back_button:
        st.session_state['show_register'] = False
        st.rerun()
    
    try:
        registration_result = authenticator.register_user(location='main')
        if registration_result is not None:
            email, username, name = registration_result
            if email:
                st.success('User registered successfully')
                # Save updated config
                with open(CONFIG_FILE, 'w') as file:
                    yaml.dump(config, file, default_flow_style=False)
                # Automatically switch back to login view
                st.session_state['show_register'] = False
                st.rerun()
    except Exception as e:
        st.error(e)

else:
    # Login Page or Main Content based on authentication status
    # Login
    authenticator.login(location='main')
    
    # Check authentication status and display appropriate content
    if st.session_state.get('authentication_status'):
        # User is authenticated - show main content
        authenticator.logout('Logout', 'main')
        st.write(f'Welcome *{st.session_state.get("name")}*')
        
    elif st.session_state.get('authentication_status') is False:
        # Authentication failed
        st.error('Username/password is incorrect')
        # Show register option
        st.write("Don't have an account?")
        register_button = st.button("Register")
        if register_button:
            st.session_state['show_register'] = True
            st.rerun()
        
    elif st.session_state.get('authentication_status') is None:
        # Not yet attempted to login
        st.warning('Please enter your username and password')
        # Show register option
        st.write("Don't have an account?")
        register_button = st.button("Register")
        if register_button:
            st.session_state['show_register'] = True
            st.rerun()