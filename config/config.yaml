app_title: " VideoRag"
implementation_name: "VideoRag Chatbot v1.0"


video_clip_storage : "./clips"

similarity_threshold : 0.5

video_splitter:
  clip_size: 10
  clip_overlap: 2

retrieval:
  k_similarity: 10

embeddings:
  embedding_model: "text-embedding-3-small"

chroma:
  collection_name: "horizon_conversation"
  persist_directory: "./chroma_store"

llm:
  openai_api_key_env_var: "83fbAYH1eWkbEAaCRmubWuJPMBPKt3sa9MdBm60TGxJJJgbJeVzbJQQJ99AKACYeBjFXJ3w3AAABACOG9tjp"
  openai_endpoint_env_var: "https://sme-code-auzre-openai.openai.azure.com"
  openai_api_version_env_var: "2024-07-01-preview"
  openai_temperature: 0
  gemini_api_key: ""
  gemini_generation_model: ""
  langfuse_secret_key: "******************************************"
  langfuse_public_key: "pk-lf-9b951014-798a-4e7e-9858-181ef3eca8b2"
  langfuse_host : "http://localhost:3000"
  langfuse_project_id : "cmcopr6eo0006qm07u901bk4w"

ports:
  host_port: 8501
  container_port: 8501

initial_message: # Content for initial assistant message
  greeting: "Hi, I'm your RAG-based chatbot! Feel free to ask me anything related to RAG systems:"
  suggested_questions:
    - "What are basic components of a RAG system"
  closing: "Let me know how I can assist you!"


