
import streamlit as st
import os

import yaml

st.title("ACEN Monitoring Dashboard 📊")

st.write(
    """
    Click the button below to open your Langfuse project dashboard in a new tab.
    Here you can monitor, debug, and evaluate your LLM application's traces,
    generations, costs, and performance.
    """
)
#Load config
with open('config/config.yaml', "r") as f:
    config = yaml.safe_load(f)

LANGFUSE_HOST = config['llm']['langfuse_host']
LANGFUSE_PROJECT_ID = config['llm']['langfuse_project_id']

langfuse_dashboard_url = f"{LANGFUSE_HOST}/project/{LANGFUSE_PROJECT_ID}"

# Use st.link_button to create a clickable button that redirects
st.link_button(
    label="Go to ACEN Dashboard",
    url=langfuse_dashboard_url,
    help="Opens the ACEN Monitoring  dashboard in a new browser tab.",
    type="primary"
)

st.info(
    "Make sure you are logged into your Langfuse account in your browser to view the dashboard."
)