import os
import re
import time
import urllib.parse
from collections import deque
from typing import List, Literal, Optional, Union

import streamlit as st
import yaml
from dotenv import load_dotenv
from langchain.memory import ConversationBufferMemory
from pydantic import BaseModel

from processing import Streamer, generate_response
from utils import clean_and_format_citations, scroll_to_bottom,expand_abbreviations

# body
load_dotenv()
# CSS Styling - Copied from ui_code.py
st.markdown(
    """
    <style>
    /* Hide the Streamlit toolbar (Deploy, Settings, etc.) */
    header { visibility: hidden; }

    /* Chatbot title styling */
    .title-container {
        text-align: center;
        margin-top: 0px;
        padding-bottom: 10px;
    }

    /* Text Input Styling */
    div[data-baseweb="input"] > div {
        background-color: #FFF3E0 !important; /* Light orange */
        border-radius: 8px !important;
    }
    .sidebar-footer {
        padding-top: 250px;
    }
    /* Style for chat input box */
    [data-testid="stChatInput"] textarea {
        background-color: #FFF3E0 !important; /* Light yellow tint */
        border-radius: 10px !important; /* Rounded corners */
    }
    .st-emotion-cache-1c7y2kd {
    flex-direction: row-reverse;
    text-align: right;
    background-color: #faf7f2 !important; /* Light yellow tint */
    }
    /* Make the popover container (the outer window) wider */
    div[data-testid="stPopoverBody"] {
        width: 700px !important;
        max-width: 700px !important;
    }

    /* Also make the inside content wider */
    div[data-testid="stPopoverContent"] {
        width: 100% !important;
        max-width: 100% !important;
    }

    /* Make expanders inside full width */
    div[data-testid="stExpander"] {
        width: 100% !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)


def load_config(config_file_path="./ui_config.yaml"):
    """Loads configuration from a YAML file."""
    try:
        with open(config_file_path, "r") as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"Error: Configuration file not found at '{config_file_path}'.")
        return None
    except yaml.YAMLError as e:
        print(f"Error parsing YAML configuration file: {e}")
        return None


def stream_response(query: str, user_name: str):
    streamer = Streamer()
    st.write_stream(streamer.generate_stream(query, user_name))
    response = streamer.generate_answer(user_name)
    return response

# Load config and user_id
config = load_config()
user_name = st.session_state.get("username", "User")

# Define avatars
avatars = {"user": "assets/User Icon.png", "assistant": "assets/Chatbot Icon.png"}
st.markdown(
    f"""
    <div class="title-container">
        <h1>{config['app_title']}</h1>
    </div>
    """,
    unsafe_allow_html=True,
)

if "memory" not in st.session_state:
    st.session_state.memory = ConversationBufferMemory(
        memory_key="chat_history",
        return_messages=True,
        buffer=deque(maxlen=4)
    )
# --- Initial Assistant Message ---
initial_message_content = f"""<p style='font-size:18px; text-align: left; color: grey;'>{config['initial_message']['greeting']}<br></i></p>
<p style='font-size:15px; text-align: left; color: grey; padding-left : 20px'>"""



if "messages" not in st.session_state or st.session_state.messages ==[]:
    st.session_state.messages = []
    with st.chat_message("assistant", avatar=avatars["assistant"]):
        st.markdown(initial_message_content, unsafe_allow_html=True)

# Render existing chat history
for msg in st.session_state.messages:
    with st.chat_message(msg["role"], avatar=avatars.get(msg["role"])):
        st.markdown(msg["content"], unsafe_allow_html=True)

# User input
user_input = st.chat_input("Type your query and press Enter...")
if user_input:
    st.session_state.messages.append({"role": "user", "content": user_input})
    with st.chat_message("user", avatar=avatars["user"]):
        st.markdown(user_input)
    # Expand abbreviations
    user_input = expand_abbreviations(user_input)
    # Generate response 
    with st.spinner("Generating response..."):
        # Process query and get structured response
        if not config['stream']:
            response = generate_response(user_input, user_name)
        with st.chat_message("assistant", avatar=avatars["assistant"]):
            if config['stream']:
                response = stream_response(user_input, user_name)
            else:
                st.markdown(response.answer)
        st.session_state.messages.append({"role": "assistant", "content": response.answer})

        # Display citations in popover
        if len(response.citations) > 0:
            with st.popover("Citations"):
                for cit in response.citations:  
                    title = f"- **Document:** {cit.document_name}"
                    if hasattr(cit, 'page_number') and cit.page_number is not None:
                        title += f", **Page:** {cit.page_number}"
                    with st.expander(title):
                        # decode URL once
                        decoded_path = None
                        if cit.source_url:
                            decoded_path = urllib.parse.unquote(str(cit.source_url))

                        # video modality: embed video player
                        if cit.modality == 'video' and decoded_path:
                            st.video(decoded_path)

                        elif cit.modality == "imagelist":
                            st.write(cit.content)

                            for img_path in cit.image_paths:
                                display_path = os.path.join(
                                    os.getcwd(),
                                    "images",
                                    os.path.basename(img_path) 
                                )
                                try:
                                    with open(display_path, "rb") as f:
                                        img_bytes = f.read()
                                    # render each image in its own row
                                    st.image(img_bytes, use_container_width=True)
                                except Exception as e:
                                    st.error(f"⚠️ Couldn't load {display_path}: {e}")


                        # image modality: embed image
                        elif cit.modality == 'image' and decoded_path:
                            st.image(decoded_path, caption=f"{cit.document_name}", use_column_width=True)
                            # show bounding box info if available
                            if getattr(cit, 'bounding_box', None):
                                st.markdown(f"**Bounding Box:** {cit.bounding_box}")

                        else:
                            # for other modalities (text/html), if there's a URL link, show it
                            if decoded_path:
                                href = f'<a href="{decoded_path}" target="_blank">Source</a>'
                                st.markdown(href, unsafe_allow_html=True)

                        # finally, show content (if any)
                        if cit.content:
                            st.markdown(f"{clean_and_format_citations(cit.content)}", unsafe_allow_html=True) 

