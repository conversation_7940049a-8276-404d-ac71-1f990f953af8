{"cells": [{"cell_type": "code", "execution_count": null, "id": "7400eebe", "metadata": {}, "outputs": [], "source": ["# import fitz\n", "import re\n", "import tiktoken  # Or use len(text.split()) if you don't need exact token count\n", "from langchain_openai import AzureChatOpenAI, AzureOpenAIEmbeddings\n", "import base64\n", "import os\n", "\n", "azure_api_key = os.getenv('OPENAI_API_KEY')\n", "azure_api_version = \"2024-07-01-preview\"\n", "azure_endpoint = \"https://sme-code-auzre-openai.openai.azure.com\"\n", "azure_deployment_name = \"gpt-4o-mini\"\n", "azure_embedding_model = \"text-embedding-3-small\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "b0f14777", "metadata": {}, "outputs": [], "source": ["from openai import AzureOpenAI\n", "\n", "\n", "class GPTCall():\n", "    def __init__(self, version, endpoint, api_key):\n", "        # api_key = os.getenv('MY_API_KEY')\n", "        self.model = AzureOpenAI(\n", "            api_key=api_key, api_version=version, azure_endpoint=endpoint\n", "        )\n", "\n", "    def openai_api_call(self, model, messages):\n", "\n", "        chat_completion = self.model.chat.completions.create(\n", "            model=model, messages=messages  # model = \"deployment_name\".\n", "        )\n", "        return chat_completion.choices[0].message.content, chat_completion.usage\n", "\n", "\n", "def encode_image(image_path):\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "model = GPTCall(azure_api_version, azure_endpoint, azure_api_key)"]}, {"cell_type": "code", "execution_count": null, "id": "314bf2c8", "metadata": {}, "outputs": [], "source": ["from tabulate import tabulate\n", "import textwrap\n", "\n", "def clean_and_wrap_table(table, wrap_width=80):\n", "    cleaned = []\n", "    for row in table:\n", "        # Skip rows that are entirely empty or None\n", "        if not any(cell and str(cell).strip() for cell in row):\n", "            continue\n", "        # Clean and wrap each cell\n", "        cleaned_row = []\n", "        for cell in row:\n", "            if cell:\n", "                cell_str = str(cell).replace('\\n', ' ')\n", "                wrapped = textwrap.fill(cell_str, width=wrap_width)\n", "                cleaned_row.append(wrapped)\n", "            else:\n", "                cleaned_row.append('')\n", "        cleaned.append(cleaned_row)\n", "    return cleaned"]}, {"cell_type": "code", "execution_count": null, "id": "a3cfa6dc", "metadata": {}, "outputs": [], "source": ["import concurrent.futures\n", "import threading\n", "from collections import defaultdict\n", "\n", "\n", "def call_model_with_timeout(model, deployment_name, messages):\n", "    return model.openai_api_call(deployment_name, messages)"]}, {"cell_type": "code", "execution_count": null, "id": "c78aa9bc", "metadata": {}, "outputs": [], "source": ["import fitz\n", "import pdfplumber\n", "import io\n", "from PIL import Image\n", "\n", "l_ = []\n", "\n", "def group_words_into_lines(words, tolerance=2, no_of_lines=5):\n", "    \"\"\"Group words into lines based on vertical alignment (Y-coordinate).\"\"\"\n", "    lines = defaultdict(list)\n", "\n", "    for word in words:\n", "        y_key = round(word['bottom'] / tolerance) * tolerance\n", "        lines[y_key].append(word)\n", "\n", "    # Sort lines from bottom up (closer to table first)\n", "    sorted_lines = sorted(lines.items(), key=lambda x: -x[0])[-no_of_lines:]\n", "\n", "    # Join words in each line left to right\n", "    text_lines = []\n", "    for _, words_in_line in sorted_lines:\n", "        words_in_line_sorted = sorted(words_in_line, key=lambda w: w['x0'])\n", "        line_text = \" \".join(w['text'] for w in words_in_line_sorted)\n", "        text_lines.append(line_text)\n", "\n", "    return text_lines\n", "\n", "def get_table_with_caption(page):\n", "    caption_table_pairs = []\n", "\n", "    tables_with_bbox = page.find_tables()\n", "    for table_info in tables_with_bbox:\n", "        table = table_info.extract()\n", "        x0 = table_info.bbox[0]\n", "        y0 = table_info.bbox[1]  # top of the table\n", "        x1 = table_info.bbox[2]\n", "        y1 = table_info.bbox[3]  # end of the table\n", "\n", "        # Get words above the table\n", "        words_above = [\n", "            w for w in page.extract_words()\n", "            if w['bottom'] < y0 and len(w['text'].strip()) > 0\n", "        ]\n", "\n", "        # Group words into lines and sort by closeness to table\n", "        caption_lines = group_words_into_lines(words_above)\n", "\n", "        cleaned_table = clean_and_wrap_table(table)\n", "        title = '\\n'.join(caption_lines[::-1])\n", "        structured_table = tabulate(cleaned_table, tablefmt=\"grid\")\n", "\n", "\n", "\n", "        if len(structured_table):\n", "            prompt_table_des = f\"\"\"You are an assistant tasked with generating table_description. Generate a description for the following table/tables.\n", "\n", "{title}\n", "{structured_table}\n", "\"\"\"\n", "\n", "            messages=[{\n", "                  \"role\": \"user\",\n", "                  \"content\": [\n", "                    {\n", "                      \"type\": \"text\",\n", "                      \"text\": prompt_table_des,\n", "                    }\n", "                  ],\n", "                }]\n", "            # try:\n", "            #   des = model.openai_api_call(azure_deployment_name, messages)\n", "            #   caption_table_pairs.append((title, table, des[0], structured_table))\n", "            # except:\n", "            #   caption_table_pairs.append((title, table, '', structured_table))\n", "\n", "            try:\n", "              with concurrent.futures.ThreadPoolExecutor() as executor:\n", "                      future = executor.submit(call_model_with_timeout, model, azure_deployment_name, messages)\n", "                      des, cost = future.result(timeout=30)  # Timeout in seconds\n", "                      caption_table_pairs.append((title, table, des, structured_table, (x0,y0,x1,y1)))\n", "\n", "            except concurrent.futures.TimeoutError:\n", "                  print(f\"Timeout occurred\")\n", "                  caption_table_pairs.append((title, table, '', structured_table, (x0,y0,x1,y1)))\n", "            except Exception as e:\n", "                    print( e)\n", "                    caption_table_pairs.append((title, table, '', structured_table, (x0,y0,x1,y1)))\n", "\n", "    return caption_table_pairs\n", "\n", "\n", "\n", "def extract_from_pdf(pdf_path, output_images_dir = 'data/images', margin=50):\n", "    pages_info = []\n", "    document_name = pdf_path.split('/')[-1][:-4]\n", "\n", "    # 2. Create the directory if it doesn't exist\n", "    os.makedirs(output_images_dir, exist_ok=True)\n", "\n", "    # Tables with pdfplumber\n", "    with pdfplumber.open(pdf_path) as pdf:\n", "        for i, page in enumerate(pdf.pages):\n", "            print(i)\n", "            tables = get_table_with_caption(page)\n", "\n", "\n", "            pages_info.append({\"table\": tables})\n", "            # pages_info[i]['text'] = '\\n'.join([i[2] for i in tables]) + pages_info[i]['text']\n", "\n", "\n", "    # Text and images with fitz\n", "    doc = fitz.open(pdf_path)\n", "    for page_num, page in enumerate(doc):\n", "        print(page_num)\n", "        page_info = {}\n", "        extracted_text = ''\n", "        raw_text = ''\n", "        tabularized_text = \"\"\n", "        images = {}\n", "        page = doc[page_num]\n", "        # Extract text blocks and images\n", "\n", "        # extracted_text = page.get_text().strip()\n", "\n", "        blocks = page.get_text(\"blocks\")  # returns list of (x0, y0, x1, y1, text, block_no, line_no, block_type)\n", "        sorted_blocks = sorted(blocks, key=lambda b: (b[1], b[0]))  # sort top to bottom, then left to right\n", "        # try:\n", "        #     print('*********',pages_info[page_num][\"table\"][0][4])\n", "        # except:\n", "        #     print('*********',pages_info[page_num][\"table\"])\n", "        # print(sorted_blocks)\n", "\n", "        raw_text = \"\\n\".join(block[4] for block in sorted_blocks)\n", "        table_dims = pages_info[page_num][\"table\"]\n", "        if len(table_dims):\n", "            for table_dim in table_dims:\n", "              if table_dim:\n", "                table_coords = table_dim[4]\n", "                added = 0\n", "                for block in sorted_blocks:\n", "                    # print(block)\n", "                    # print(table_coords)\n", "                    if block[3]<table_coords[1]:\n", "                        tabularized_text += block[4]\n", "                    if block[1] > table_coords[1] and block[3] < table_coords[3]:\n", "                        if added == 0:\n", "                            # print('heyyyy2')\n", "                            tabularized_text += table_dim[3]\n", "                            # print(tabularized_text)\n", "\n", "                            added=1\n", "                        else:\n", "                            continue\n", "                    if block[1] > table_coords[3]:\n", "                        tabularized_text += block[4]\n", "        else:\n", "            tabularized_text = raw_text\n", "\n", "\n", "        image_list = page.get_images(full=True)\n", "        for image_index, img in enumerate(image_list, start=0):\n", "            xref = img[0]  # Get the XREF of the image\n", "            # print(\"==========\",xref)\n", "            base_image = doc.extract_image(xref)  # Extract the image\n", "            image_bytes = base_image[\"image\"]  # Get the image bytes\n", "            image_ext = base_image[\"ext\"]  # Get the image extension\n", "            # Load the image using PIL and save it\n", "            image = Image.open(io.BytesIO(image_bytes))\n", "            # Locate the image's bounding box on the page\n", "            rects = page.get_image_rects(xref)\n", "            # Assume the first rectangle corresponds to the image\n", "            bbox = rects[0]\n", "            # Expand the bounding box by a margin\n", "            expanded_bbox = fitz.Rect(\n", "                max(bbox.x0 - margin, 0),\n", "                max(bbox.y0 - (margin + 25), 0),\n", "                min(bbox.x1 + margin, page.rect.width),\n", "                min(bbox.y1 + (margin+25), page.rect.height)\n", "            )\n", "            if abs(bbox.y0 - bbox.y1) < 25 or abs(bbox.y1 - bbox.y0) <25 or abs(bbox.x1 - bbox.x0) < 25 or abs(bbox.x0 - bbox.x1) < 25:\n", "                # If the height of the bounding box is less than 25, skip this image\n", "                print(f\"Skipping image {image_index} on page {page_num + 1} due to small bounding box size.\")\n", "                continue\n", "            # Extract the expanded region as a Pixmap\n", "            pix = page.get_pixmap(clip=expanded_bbox)\n", "            filename = f\"{output_images_dir}/{document_name}_image_{page_num+1}_{image_index}.{image_ext}\"\n", "\n", "            pix.save(filename)\n", "\n", "            # # Caption the images using LLM\n", "            base64_image = encode_image(filename)\n", "            prompt = \"\"\"You are an assistant tasked with summarizing images/charts for retrieval if the image is a chart.\n", "These summaries will be embedded and used to retrieve the raw text\n", "Give a concise summary of the image that is well optimized for retrieval.\n", "GUIDELINES:\n", "- Provide clear description of the information in the image provided below.\n", "- DESCRIBE THE ENTIRE IMAGE CLEARLY. DO NOT MISINTERPRET/LOSE DETAILS.\n", "- IMPORTANT: NO INFORMATION MUST BE LOST\n", "- IMPORTANT: NO INFORMATION MUST BE MISINTERPRETED.\n", "- IMPORTANT: ALL THE NUMBERS MUST BE INTACT.\n", "- If a chart is present in the image, provide description. If any image other than a chart(ex: pie chart, bar chart, line chart) is present, return \"NOT USEFUL IMAGE\"\n", "\n", "image:\n", "\"\"\"\n", "            messages=[{\n", "              \"role\": \"user\",\n", "              \"content\": [\n", "                {\n", "                  \"type\": \"text\",\n", "                  \"text\": prompt,\n", "                },\n", "                {\n", "                  \"type\": \"image_url\",\n", "                  \"image_url\": {\n", "                    \"url\":  f\"data:image/jpeg;base64,{base64_image}\"\n", "                  },\n", "                },\n", "              ],\n", "            }]\n", "            # try:\n", "\n", "            #     response, cost = model.openai_api_call(azure_deployment_name , messages)\n", "            #     print(filename, \":\", response)\n", "            #     if 'not useful'  not in response.lower():\n", "            #         extracted_text += '\\n\\n' + '<image>' + filename + '\\n\\n' + response + '\\n\\n' + '</image>'\n", "            # except:\n", "            #     print(filename)\n", "            try:\n", "              with concurrent.futures.ThreadPoolExecutor() as executor:\n", "                      future = executor.submit(call_model_with_timeout, model, azure_deployment_name, messages)\n", "                      response, cost = future.result(timeout=30)  # Timeout in seconds\n", "                      print(filename, \":\", response)\n", "\n", "                      if 'not useful' not in response.lower():\n", "                          extracted_text = raw_text + '\\n\\n' + '<image>' + filename + '\\n\\n' + response + '\\n\\n' + '</image>'\n", "                          images[filename]  = response\n", "            except concurrent.futures.TimeoutError:\n", "                  print(f\"{filename}: Timeout occurred\")\n", "            except Exception as e:\n", "                    print(filename, \":\", e)\n", "\n", "        page_info = {\"text\": extracted_text, \"raw_text\": raw_text, \"tabularized_text\": tabularized_text, \"page_number\": page_num+1, \"document_name\": document_name, \"images\": images}\n", "        pages_info[page_num] = {**pages_info[page_num],**page_info}\n", "\n", "    doc.close()\n", "    \n", "    return pages_info,l_\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2d4290eb", "metadata": {}, "outputs": [], "source": ["# import fitz  # PyMuPDF\n", "\n", "rotated_pages = []\n", "overflow_tables = [[1,2,3,4], [7,8], [8,9],[14,15]]\n", "\n", "\n", "# # rotate 65, 66 and 75\n", "# doc = fitz.open('/mnt/d/PPAC/Data/1747978983_Final_Ready_Reckoner_FY_2024-25_Ver01.pdf')\n", "# for page_num in rotated_pages:\n", "#     page = doc[page_num-1]  # page index 1 = page 2\n", "#     page.set_rotation(360)  # rotate 90 degrees clockwise\n", "# doc.save(\"rotated_output.pdf\")\n", "\n", "\n", "d, l_ = extract_from_pdf('data/1_MEDICARD.pdf')\n", "# # d, l_ = extract_from_pdf(\"/mnt/c/Users/<USER>/Downloads/ATK_Demo_Trim.pdf\")\n", "# d, l_ = extract_from_pdf(\"rotated_output.pdf\")\n", "# # d"]}, {"cell_type": "code", "execution_count": null, "id": "dcae196f", "metadata": {}, "outputs": [], "source": ["# import json\n", "# with open(\"chunks_4_19_06.json\", \"w\") as f:\n", "#     json.dump(d, f, indent=4)"]}, {"cell_type": "code", "execution_count": null, "id": "9ae7ce67", "metadata": {}, "outputs": [], "source": ["import json\n", "with open(\"chunks_4_19_06.json\", \"r\", encoding=\"utf-8\") as f:\n", "    d = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "187c472a", "metadata": {}, "outputs": [], "source": ["page_num = 112\n", "images_text = '\\n\\n'.join(['<image>' + '\\n\\n' + i + '\\n\\n' + d[page_num]['images'][i] + '</image>' for i in d[page_num]['images']] )\n", "print(d[page_num]['table'][0][2] + '\\n\\n' +d[page_num]['tabularized_text'] + '\\n\\n' + images_text)"]}, {"cell_type": "code", "execution_count": null, "id": "449943a6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "03678ce1", "metadata": {}, "outputs": [], "source": ["import tiktoken\n", "def count_tokens(text):\n", "    enc = tiktoken.encoding_for_model(\"gpt-3.5-turbo\")\n", "    return len(enc.encode(text))\n"]}, {"cell_type": "code", "execution_count": null, "id": "186dc0c6", "metadata": {}, "outputs": [], "source": ["import chromadb\n", "\n", "class EmbeddingFunction:\n", "    def __init__(self, model):\n", "        self.model = model\n", "\n", "    def __call__(self, input):\n", "        return self.model.encode(input).tolist()\n", "\n", "\n", "def create_chroma_db( collection_nae, chunks, overflow_pages_list):\n", "    # Initialize Chroma client with default settings\n", "    print(\"===============1\", len(chunks))\n", "\n", "    chroma_client = chromadb.PersistentClient(path=\"rag_store\")\n", "\n", "\n", "    embeddings_model = AzureOpenAIEmbeddings(\n", "        openai_api_key=azure_api_key,\n", "        openai_api_version=azure_api_version,\n", "        azure_endpoint=azure_endpoint,\n", "        model=azure_embedding_model\n", "    )\n", "\n", "    embedding_function = EmbeddingFunction(embeddings_model)\n", "\n", "    collection = chroma_client.create_collection(\n", "        name=collection_nae,\n", "        embedding_function=embedding_function\n", "    )\n", "\n", "    new_chunks = []\n", "    # Batch embed all chunks for current file\n", "    for idx,chunk in enumerate(chunks):\n", "        overflow = [i for i in overflow_pages_list if chunk['page_number'] in i]\n", "        # print(overflow)\n", "        if overflow and chunk['page_number'] == overflow[0][0]:\n", "            images_text = []\n", "            table_descriptions = []\n", "            tabularized_text = ''\n", "            for j in overflow[0]:\n", "                images_text += ['<image>' + '\\n\\n' + i + '\\n\\n' + chunks[j-1]['images'][i] + '</image>' for i in chunks[j-1]['images']]\n", "                table_descriptions += [i[2] for i in chunks[j-1]['table']]\n", "                tabularized_text += '\\n' + chunks[j-1]['tabularized_text']\n", "\n", "                # print(len(images_text), len(table_descriptions), len(tabularized_text))\n", "                # add these for all the combined pages if necessary - 'table', 'text', 'raw_text', 'tabularized_text', 'page_number', 'document_name', 'images', 'doc_text'\n", "\n", "            chunks[idx] = {**chunks[idx],\n", "                           \"elements\":[*table_descriptions, tabularized_text, *images_text],\n", "                           \"doc_text\":'\\n\\n' + tabularized_text + '\\n\\n' + '\\n\\n'.join(images_text),\n", "                           \"page_number\": str(overflow[0])\n", "                           }\n", "\n", "        if overflow and chunk['page_number'] in overflow[0] and chunk['page_number'] != overflow[0][0]:\n", "            continue\n", "\n", "\n", "        if len(overflow) == 0:\n", "            images_text = ['<image>' + '\\n\\n' + i + '\\n\\n' + chunk['images'][i] + '</image>' for i in chunk['images']]\n", "            table_descriptions = [i[2] for i in chunk['table']]\n", "            chunks[idx] = {**chunks[idx],\n", "                           \"elements\":[*table_descriptions, chunk['tabularized_text'], *images_text],\n", "                           \"doc_text\":'\\n\\n' +chunk['tabularized_text'] + '\\n\\n' + '\\n\\n'.join(images_text)\n", "                           }\n", "        for element in chunks[idx][\"elements\"]:\n", "            new_chunks.append({\"element\": element, **chunks[idx]})\n", "\n", "    print(len(new_chunks))\n", "\n", "    chunk_embeddings = embeddings_model.embed_documents([i[\"element\"] for i in new_chunks])\n", "    # Store chunks with embeddings in ChromaDB\n", "    for idx, (chunk, emb) in enumerate(zip(new_chunks, chunk_embeddings)):\n", "        collection.add(\n", "            ids=[str(idx)],\n", "            embeddings=[emb],\n", "            metadatas=[{\"id\": idx,\n", "             \"document_name\": chunk[\"document_name\"],\n", "             \"page_number\": chunk[\"page_number\"],\n", "              \"images\": str(chunk[\"images\"]),\n", "              \"tabularized_text\": chunk[\"tabularized_text\"],\n", "               \"raw_text\": chunk[\"raw_text\"],\n", "                \"table\": str(chunk.get(\"table\", []))}],\n", "            documents=[chunk['doc_text']]\n", "        )\n", "\n", "    print(\"ChromaDB vector store created successfully.\")\n", "    return collection\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf15bd1c", "metadata": {}, "outputs": [], "source": ["print(d[10]['tabularized_text'])"]}, {"cell_type": "code", "execution_count": null, "id": "6dccbda7", "metadata": {}, "outputs": [], "source": ["chunks_ = []\n", "    # Batch embed all chunks for current file\n", "for idx,chunk in enumerate(d):\n", "        images_text = '\\n\\n'.join(['<image>' + '\\n\\n' + i + '\\n\\n' + chunk['images'][i] + '</image>' for i in chunk['images']] )\n", "        d[idx] = { **d[idx], \"doc_text\":  '\\n\\n' +chunk['tabularized_text'] + '\\n\\n' + images_text}\n", "\n", "print(d[64]['table'])"]}, {"cell_type": "code", "execution_count": null, "id": "46cef49f", "metadata": {}, "outputs": [], "source": ["create_chroma_db(\"kearney_32\", d,overflow_tables)"]}, {"cell_type": "code", "execution_count": null, "id": "929981e3", "metadata": {}, "outputs": [], "source": ["def load_chroma_db(collection_name, persist_directory=\"/mnt/d/PPAC/initial exploration/rag_store\"):\n", "    \"\"\"\n", "    Load ChromaDB vector store from disk.\n", "\n", "    Args:\n", "        persist_directory: Directory where the database is stored\n", "\n", "    Returns:\n", "        ChromaDB collection\n", "    \"\"\"\n", "    try:\n", "        # Initialize Chroma client with the persist_directory\n", "        chroma_client = chromadb.PersistentClient(path=persist_directory)\n", "        # Retrieve the collection (assumes it exists already)\n", "        collection = chroma_client.get_or_create_collection(name=collection_name)\n", "        #print(f\"Successfully loaded ChromaDB collection: {collection}\")\n", "        return collection\n", "    except Exception as e:\n", "        raise RuntimeError(f\"Error loading ChromaDB: {str(e)}\")\n", "\n", "def query_top_chunks(query_text: str, collection_name, persist_directory=r\"rag_store\", top_k=5):\n", "    \"\"\"\n", "    Get the relevant information for the asked query.\n", "\n", "    Args:\n", "        query_text: The query to search for\n", "        persist_directory: Directory where the database is stored\n", "        top_k: Number of top results to return\n", "\n", "    Returns:\n", "        List of relevant text chunks\n", "    \"\"\"\n", "    try:\n", "        # Step 1: Load the existing ChromaDB vector store\n", "        collection = load_chroma_db(collection_name,persist_directory)\n", "        if not collection:\n", "            return [\"Error: ChromaDB collection not found\"]\n", "\n", "        # Load embedding model\n", "        embeddings_model = AzureOpenAIEmbeddings(\n", "        openai_api_key=azure_api_key,\n", "        openai_api_version=azure_api_version ,\n", "        azure_endpoint=azure_endpoint,\n", "        model=azure_embedding_model\n", "        )\n", "\n", "\n", "        # Generate embedding for the query text\n", "        query_embedding =  embeddings_model.embed_documents([query_text])\n", "        # print(query_embedding[:15])\n", "        # Query the collection for similar chunks\n", "        results = collection.query(\n", "            query_embeddings=query_embedding,\n", "            n_results=10+top_k\n", "        )\n", "        cleaned_results = {}\n", "        documents = []\n", "        metadatas = []\n", "        for idx, doc in enumerate(results[\"documents\"][0]):\n", "            if doc not in documents:\n", "                documents.append(doc)\n", "                metadatas.append(results['metadatas'][0][idx])\n", "\n", "        cleaned_results['documents'] = [documents[:top_k]]\n", "        cleaned_results['metadatas'] = [metadatas[:top_k]]\n", "\n", "        return cleaned_results\n", "\n", "\n", "    except Exception as e:\n", "        return [f\"Error querying chunks: {str(e)}\"]\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "f5cf3211", "metadata": {}, "outputs": [], "source": ["# print(d[28]['doc_text'])\n", "print(d[30]['doc_text'])\n", "# print(d[35]['doc_text'])"]}, {"cell_type": "code", "execution_count": null, "id": "371e9dcc", "metadata": {}, "outputs": [], "source": ["retrieved_chunks = query_top_chunks(\"how many blocks were nominated by ongc in 2024-25\", collection_name = \"kearney_32\", persist_directory=r\"rag_store\", top_k=3)\n", "print([i['page_number'] for i in retrieved_chunks['metadatas'][0]])\n", "# [21, 19, 20, 3, 14, 59, 20, 1, 108, 58]\n", "len(retrieved_chunks['documents'][0]), len(retrieved_chunks['metadatas'][0])\n"]}, {"cell_type": "code", "execution_count": null, "id": "5e5dd913", "metadata": {}, "outputs": [], "source": ["# print(retrieved_chunks['documents'][0][0])\n", "# retrieved_chunks['metadatas'][0]\n", "import ast\n", "for match_doc, match_metadata in zip(retrieved_chunks['documents'][0], retrieved_chunks['metadatas'][0]):\n", "    # print(match_doc.split()[:200])\n", "    # print(match_doc.split()[-200:])\n", "    # print('---------------')\n", "    # print(match_metadata)\n", "    # print(\"----------------\")\n", "    curr_tables = ast.literal_eval(match_metadata['table'])\n", "    if curr_tables:\n", "        for table in curr_tables:\n", "            table_start = '\\n'.join([i for i in table[1][0] + table[1][1] if i is not None])\n", "            table_end = '\\n'.join([i for i in table[1][-2] + table[1][-1] if i is not None])\n", "            print(match_doc.split()[:len(table_start.split())])\n", "            print(table_start.split())\n", "            print(match_doc.split()[:len(table_start.split())] == table_start.split())\n", "\n", "    print(\"============\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b118816", "metadata": {}, "outputs": [], "source": ["import time\n", "import ast\n", "\n", "\n", "def get_context(match):\n", "      match['documents']\n", "\n", "\n", "def get_response(query, history = [], top_k = 3, cached_file = None):\n", "\n", "        # if history != []:\n", "        #     history = get_history(history, 5)\n", "\n", "        responses = []\n", "        costs = []\n", "        latency = {}\n", "        contexts = []\n", "        pages = []\n", "\n", "        retrieval_start = time.time()\n", "        top_matches = query_top_chunks(query, collection_name = \"kearney_32\", persist_directory=r\"rag_store\", top_k=top_k)\n", "        latency['retrieval_time'] = time.time()-retrieval_start\n", "        latency['llm_call'] = []\n", "\n", "        # context_ = [{\n", "        #     'page_num':  int(i.metadata[\"name\"].split('/')[-1].split('.')[0].split('_')[1]) if type(i.metadata[\"name\"])== str else i.metadata[\"name\"],\n", "        #     'context': i.page_content\n", "        # } for i in top_matches]\n", "        # context = '\\n\\n'.join([str(i) for i in context_])\n", "\n", "        # images = [i.metadata[\"name\"] for i in top_matches]\n", "        context = \"\"\n", "        for top_doc, top_metadata in zip(top_matches['documents'][0], top_matches['metadatas'][0]):\n", "            print(top_metadata)\n", "            images = ast.literal_eval(top_metadata['images'])\n", "            table = ast.literal_eval(top_metadata['table'])\n", "\n", "            context += top_doc\n", "            context+=\"\\n\\n===================================\\n\\n\"\n", "\n", "\n", "            pages.append(top_metadata[\"page_number\"])\n", "        contexts.append(context)\n", "\n", "#         prompt = f\"\"\" You are a helpful assistant who answers the given question from the context provided below.\n", "\n", "# GUIDELINES:\n", "# - Answer the question from the context provided below.\n", "# - Do not assume anything.\n", "# - If the answer cannot be answered from the context, return \"I don't know\"\n", "\n", "# QUESTION: {query}\n", "\n", "# CONTEXT: {context}\n", "\n", "# ANSWER:\n", "\n", "# \"\"\"\n", "        prompt = f\"\"\" You are a helpful assistant who answers the given query from the context provided below.\n", "Understand the context carefully and repond with the right answer\n", "\n", "QUERY: {query}\n", "\n", "CONTEXT: {context}\n", "\n", "ANSWER:\n", "\n", "\"\"\"\n", "        # print(prompt)\n", "        messages = history + [{\n", "          \"role\": \"user\",\n", "          \"content\": [\n", "            {\n", "              \"type\": \"text\",\n", "              \"text\": prompt,\n", "            },\n", "          ],\n", "        }]\n", "        start_time = time.time()\n", "        response, cost = model.openai_api_call(azure_deployment_name, messages)\n", "        end_time = time.time()\n", "        # print(end_time-start_time)\n", "        latency['llm_call'].append( end_time - start_time)\n", "        responses.append(response)\n", "        costs.append(cost)\n", "        return responses, costs, top_matches, latency, context, pages\n"]}, {"cell_type": "code", "execution_count": null, "id": "162c0a0b", "metadata": {}, "outputs": [], "source": ["l = [\"What is the GDP of India in 2023-2024\",\n", "\"What is the GDP growth rate in 24-25\",\n", "\"what were the imports and exports in 2021-22\", # bad retrieval\n", "\"what were India's foreign exchange reserves in each year from 2020 to 2025\",\n", "\"how much did coal contribute to India's energy basket in 2023\",\n", "\"what was saudi arabia's per capita nuclear energy consumption in 2023\",\n", "\"how many barrels of crude oil reserves does Brazil have in 2020\",\n", "\"how many barrles of crude oil does EU produce daily in 2023\", # tried alot of prompting to answer, \"I dont knwo\" # consumption vs production vs refining\n", "\"what was the change in Japan's crude oil refining capacity from 2013 to 2023\",\n", "\"what was the on shore total crude oil production 2020-21\", #domestic vs on shore numbers\n", "\"how many blocks were nominated by ongc in 2024-25\",  # why not working\n", "\"when was nelp-I launched and how many blocked were offered?\",\n", "\"how many total blocks were offered under nelp from 1999-2010\",\n", "\"what was the total domestic oil and gas production in 2023-24\", #bad retrieval\n", "\"Detail the policy for exploration of shale in India\",\n", "\"which country had the most natural gas reserves and how much in 2020\",\n", "\"which is the most natural gas consuming country in 2023\",\n", "\"what was the net production for sale of ongc in 2020-21\", #bad caption and confused tables\n", "\"what was the net production of natural gas by Gujarat from 2022-2025\", #confused tables\n", "\"how many commercial png in Vijaywada\", #bad retrieval, confused tables\n", "\"who is the authorized CGD entity in south goa\",\n", "\"how many CNG stations does bhagyanagar gas limited have in Kakinada\",\n", "\"list down all the geographical areas and their authorized cgd entity \",\n", "\"how many cng stations in each district of jharkhand\" #overflowing table\n", "]\n", "\n", "\n", "l = [\"What is the India’s % share in the global energy production\",\n", "\"What is the GDP of India in 2023-2024\",\n", "\"What is the GDP growth rate in 24-25\",\n", "\"what were the imports and exports in 2021-22\",\n", "\"what were India's foreign exchange reserves in each year from 2020 to 2025\",\n", "\"how much did coal contribute to India's energy basket in 2023\",\n", "\"what was saudi arabia's per capita nuclear energy consumption in 2023\",\n", "\"how many barrels of crude oil reserves does Brazil have in 2020\",\n", "\"how many barrles of crude oil does EU produce daily in 2023\",\n", "\"what was the change in Japan's crude oil refining capacity from 2013 to 2023\",\n", "\"what was the on shore total crude oil production 2020-21\",\n", "\"how many blocks were nominated by ongc in 2024-25\",\n", "\"when was nelp-I launched and how many blocked were offered?\",\n", "\"how many total blocks were offered under nelp from 1999-2010\",\n", "\"what was the total domestic oil and gas production in 2023-24\",\n", "\"Detail the policy for exploration of shale in India\",\n", "\"which country had the most natural gas reserves and how much in 2020\",\n", "\"which is the most natural gas consuming country in 2023\",\n", "\"what was the net production for sale of ongc in 2020-21\",\n", "\"what was the net production of natural gas by Gujarat from 2022-2025\",\n", "\"how many commercial png in Vijaywada\",\n", "\"who is the authorized CGD entity in south goa\",\n", "\"how many CNG stations does bhagyanagar gas limited have in Kakinada\",\n", "\"list down all the geographical areas and their authjorized cgd entity \",\n", "\"how many cng stations in each district of jharkhand\",\n", "\"how many cng stations are in dewas\",\n", "\"how many cng stations does Indraprastha Gas Limited have in delhi\",\n", "\"how many domestic png are there in tamil nadu\",\n", "\"who is the authorized CGD entity in dehradun\",\n", "\"how many total cng station in India as of March end this year?\",\n", "\"how much does domestic png contribute to CGD sales in 2023-24\",\n", "\"what was the % consumption of natural gas by manufacturing  from april 2024-march 2025\",\n", "\"What is market share of GAIL among pipeline operators as of April 25\",\n", "\"what is the share of SKO in pol consumption in 24-25\",\n", "\"% contribution of shipping to direct sales in HSD segment Apr-Mar25\",\n", "\"What has been y-o-y growth in CNG sales in Andhra Pradesh from 2020 to 2024\",\n", "\"what was the total cng sales in punjab in the last 5 years\",\n", "\"which state had the highest sales of CNG in 2024-25\",\n", "\"which 3 states had the least CNG sales in 2024-25\",\n", "\"what is the differnece in contribution of CNG in cgd sales prfile from 2023-24 to 2024-25\"]\n", "# l = [\"which state had the highest sales of CNG in 2024-25\"]\n", "\n", "resp = []\n", "costs_ = []\n", "latencies = []\n", "contexts = []\n", "pages_ = []\n", "\n", "import pandas as pd\n", "df = pd.DataFrame()\n", "for question in l:\n", "    start = time.time()\n", "    responses, costs, top_matches, latency, context, pages = get_response(question, top_k=5)\n", "    print(time.time()-start)\n", "    print(latency)\n", "    print(question)\n", "    print(responses)\n", "    resp.append(responses)\n", "    costs_.append(costs)\n", "    latencies.append(latency)\n", "    contexts.append(context)\n", "    pages_.append(pages)\n", "\n", "\n", "    print(\"==========================\\n\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "9a9e4133", "metadata": {}, "outputs": [], "source": ["page_number = 20\n", "print(d[page_number-1]['doc_text'])"]}, {"cell_type": "code", "execution_count": null, "id": "130cf165", "metadata": {}, "outputs": [], "source": ["for i in resp:\n", "    print(i)"]}, {"cell_type": "code", "execution_count": null, "id": "d9262a03", "metadata": {}, "outputs": [], "source": ["df['question'] = l\n", "df['response'] = resp\n", "df['cost'] = costs_\n", "df['latency'] = latencies\n", "df['context'] = contexts\n", "df['page'] = pages_\n", "\n", "\n", "# df.to_csv('experiment_2.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "a6871d4c", "metadata": {}, "outputs": [], "source": ["print(contexts[-1])\n", "len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "c34ddcdf", "metadata": {}, "outputs": [], "source": ["df.to_excel(\"experiment_2_unit_level_chunking_top_5_w_no_table_des_in_gen_40_questions_rotated_pages_overflow_handled.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "83533811", "metadata": {}, "outputs": [], "source": ["table = d[15]['table']\n", "\n", "cleaned_table = clean_and_wrap_table(table[0][1])\n", "title = '\\n'.join(table[0][0])\n", "print(title)\n", "print(tabulate(cleaned_table, tablefmt=\"grid\"))"]}, {"cell_type": "code", "execution_count": null, "id": "b2302199", "metadata": {}, "outputs": [], "source": ["print(d[14]['text'])"]}, {"cell_type": "code", "execution_count": null, "id": "1e23860a", "metadata": {}, "outputs": [], "source": ["from assistant import Assistant\n", "agent = Assistant()\n", "agent.run(\"hello\")"]}, {"cell_type": "code", "execution_count": null, "id": "d6e05af1", "metadata": {}, "outputs": [], "source": ["import base64\n", "import os\n", "from openai import AzureOpenAI\n", "from IPython.display import display, Markdown\n", "\n", "# --- Helper Function to Encode Image ---\n", "# This function converts an image file into a base64 string for the API call.\n", "def encode_image(image_path: str) -> str:\n", "    \"\"\"Encodes an image file to a base64 string.\"\"\"\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "# --- Main Extraction Function ---\n", "def extract_content_from_image(\n", "    image_path: str,\n", "    client: AzureOpenAI,\n", "    model_name: str\n", ") -> str:\n", "    \"\"\"\n", "    Extracts text and table content from an image using a vision model.\n", "\n", "    Args:\n", "        image_path: The file path of the image to process.\n", "        client: The initialized AzureOpenAI client.\n", "        model_name: The name of the Azure deployment (e.g., \"gpt-4o\").\n", "\n", "    Returns:\n", "        The extracted content as a string, formatted with Markdown.\n", "    \"\"\"\n", "    if not os.path.exists(image_path):\n", "        return f\"Error: Image file not found at {image_path}\"\n", "\n", "    try:\n", "        # 1. Encode the image to base64\n", "        base64_image = encode_image(image_path)\n", "\n", "        # 2. Define the powerful, flexible prompt\n", "        prompt_text = \"\"\"\n", "        You are an expert AI assistant specializing in accurate data extraction from documents.\n", "        Your task is to transcribe all content from the provided image. This includes paragraphs, lists, and tables.\n", "\n", "        GUIDELINES:\n", "        1. Transcribe all text verbatim, from top to bottom.\n", "        2. **Crucial Rule:** When you identify a table, format that specific part as a Markdown table to preserve its structure (rows and columns).\n", "        3. For all non-table text (headings, paragraphs, lists), transcribe it as plain text, maintaining the original formatting.\n", "        4. Do not summarize, interpret, or omit any information. The goal is a complete and accurate representation of the image content.\n", "        5. If the image is unreadable, respond with \"Unable to read content from image.\"\n", "        \"\"\"\n", "\n", "        # 3. Make the API call to the vision model\n", "        response = client.chat.completions.create(\n", "            model=model_name,\n", "            messages=[\n", "                {\n", "                    \"role\": \"user\",\n", "                    \"content\": [\n", "                        {\"type\": \"text\", \"text\": prompt_text},\n", "                        {\n", "                            \"type\": \"image_url\",\n", "                            \"image_url\": {\n", "                                \"url\": f\"data:image/jpeg;base64,{base64_image}\"\n", "                            },\n", "                        },\n", "                    ],\n", "                }\n", "            ],\n", "            max_tokens=4000, # Increased max_tokens for potentially dense pages\n", "        )\n", "\n", "        # 4. Return the extracted content\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        return f\"An error occurred: {e}\"\n", "\n", "\n", "# 1. CONFIGURE YOUR CLIENT \n", "client = AzureOpenAI(\n", "    api_version=azure_api_version,\n", "    azure_endpoint=azure_endpoint,\n", "    api_key=azure_api_key,\n", ")\n", "# This is the name of your model deployment in Azure AI Studio\n", "AZURE_DEPLOYMENT_NAME = \"gpt-4o-mini\"\n", "\n", "\n", "# 2. SET THE IMAGE PATH\n", "# Replace this with the actual path to your image file.\n", "# For example: 'data/images/1_MEDICARD_image_11_0.png'\n", "image_to_process = 'data/images/1_MEDICARD_image_1_0.png'\n", "\n", "\n", "# 3. CALL THE FUNCTION AND GET THE DATA\n", "print(f\"Processing image: {image_to_process}...\")\n", "extracted_content = extract_content_from_image(\n", "    image_path=image_to_process,\n", "    client=client,\n", "    model_name=AZURE_DEPLOYMENT_NAME\n", ")\n", "\n", "\n", "# 4. DISPLAY THE RETURNED DATA IN MARKDOWN FORMAT\n", "print(\"\\n--- Extracted Content ---\")\n", "display(Markdown(extracted_content))"]}, {"cell_type": "code", "execution_count": null, "id": "322a4495", "metadata": {}, "outputs": [], "source": ["import os\n", "import base64\n", "from pathlib import Path\n", "from openai import AzureOpenAI\n", "from IPython.display import display, Markdown\n", "\n", "# --- 1. Help<PERSON> to Encode Image ---\n", "def encode_image(image_path: str) -> str:\n", "    \"\"\"Encodes an image file to a base64 string.\"\"\"\n", "    with open(image_path, \"rb\") as image_file:\n", "        return base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "# --- 2. Modified Markdown Saving Function ---\n", "def save_markdown(content: str, filename: str, output_dir: str) -> None:\n", "    \"\"\"\n", "    Saves markdown content to a file in a specified directory.\n", "\n", "    Args:\n", "        content (str): Markdown content to save.\n", "        filename (str): Name of the file.\n", "        output_dir (str): Path to the output directory.\n", "    \"\"\"\n", "    try:\n", "        if not content or not filename:\n", "            raise ValueError(\"Content and filename must not be empty.\")\n", "\n", "        # Ensure .md extension\n", "        if not filename.endswith(\".md\"):\n", "            filename += \".md\"\n", "\n", "        # Set up paths\n", "        save_path = Path(output_dir)\n", "        \n", "        # Create output directory if it doesn't exist\n", "        save_path.mkdir(parents=True, exist_ok=True)\n", "        \n", "        file_path = save_path / filename\n", "\n", "        # Save the content\n", "        print(f\"Saving extracted content to: {file_path}\")\n", "        with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "            f.write(content)\n", "        print(f\"Successfully saved file: {file_path}\")\n", "\n", "    except Exception as e:\n", "        print(f\"Error saving markdown file {filename}: {str(e)}\")\n", "        raise\n", "\n", "# --- 3. Image Content Extraction Function ---\n", "def extract_content_from_image(image_path: str, client: AzureOpenAI, model_name: str) -> str:\n", "    \"\"\"\n", "    Extracts text and table content from an image using a vision model.\n", "    \"\"\"\n", "    if not os.path.exists(image_path):\n", "        return f\"Error: Image file not found at {image_path}\"\n", "    try:\n", "        base64_image = encode_image(image_path)\n", "        prompt_text = \"\"\"\n", "        You are an expert AI assistant specializing in accurate data extraction from documents.\n", "        Your task is to transcribe all content from the provided image. This includes paragraphs, lists, and tables.\n", "\n", "        GUIDELINES:\n", "        1. Transcribe all text verbatim, from top to bottom.\n", "        2. When you identify a table, format it as a Markdown table.\n", "        3. For all non-table text, transcribe it as plain text, maintaining original formatting.\n", "        4. Do not summarize or omit any information.\n", "        \"\"\"\n", "        response = client.chat.completions.create(\n", "            model=model_name,\n", "            messages=[\n", "                {\"role\": \"user\", \"content\": [\n", "                    {\"type\": \"text\", \"text\": prompt_text},\n", "                    {\"type\": \"image_url\", \"image_url\": {\"url\": f\"data:image/jpeg;base64,{base64_image}\"}},\n", "                ]}\n", "            ],\n", "            max_tokens=4000,\n", "        )\n", "        return response.choices[0].message.content\n", "    except Exception as e:\n", "        return f\"An error occurred during extraction: {e}\"\n", "\n", "# --- 4. Main Controller Function ---\n", "def process_image_and_save_markdown(image_path: str, output_dir: str, client: AzureOpenAI, model_name: str):\n", "    \"\"\"\n", "    Orchestrates extracting content from an image and saving it as a markdown file.\n", "    \"\"\"\n", "    print(f\"Starting processing for image: {image_path}\")\n", "    \n", "    # Extract content from the image\n", "    extracted_content = extract_content_from_image(image_path, client, model_name)\n", "    \n", "    # Check if extraction was successful before saving\n", "    if extracted_content.startswith(\"Error:\") or extracted_content.startswith(\"An error occurred\"):\n", "        print(extracted_content)\n", "        return\n", "\n", "    # Generate a new filename from the image path\n", "    base_name = os.path.basename(image_path)\n", "    file_stem = Path(base_name).stem\n", "    output_filename = f\"{file_stem}_extraction.md\"\n", "    \n", "    # Save the content to a markdown file\n", "    save_markdown(extracted_content, output_filename, output_dir)\n", "    \n", "    # Also display the output directly for review\n", "    print(\"\\n--- Extracted Content Preview ---\")\n", "    display(Markdown(extracted_content))\n", "\n", "# 1. CONFIGURE YOUR CLIENT \n", "client = AzureOpenAI(\n", "    api_version=azure_api_version,\n", "    azure_endpoint=azure_endpoint,\n", "    api_key=azure_api_key,\n", ")\n", "# This is the name of your model deployment in Azure AI Studio\n", "AZURE_DEPLOYMENT_NAME = \"gpt-4o-mini\"\n", "\n", "\n", "# 2. DEFINE YOUR PATHS\n", "image_to_process = 'data/images/1_MEDICARD_image_1_0.png' # <--- CHANGE THIS\n", "output_markdown_dir = 'data/markdowns'    # The desired output folder\n", "\n", "# 3. RUN THE PROCESS\n", "process_image_and_save_markdown(\n", "    image_path=image_to_process,\n", "    output_dir=output_markdown_dir,\n", "    client=client,\n", "    model_name=AZURE_DEPLOYMENT_NAME\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "41c64f74", "metadata": {}, "outputs": [], "source": ["import re\n", "import os\n", "\n", "def find_markdown_tables(markdown_text: str) -> list[str]:\n", "    \"\"\"\n", "    Finds and extracts all Markdown tables from a string of text.\n", "\n", "    Args:\n", "        markdown_text: A string containing content in Markdown format.\n", "\n", "    Returns:\n", "        A list of strings, where each string is a complete Markdown table.\n", "    \"\"\"\n", "    lines = markdown_text.strip().split('\\n')\n", "    header_separator_regex = re.compile(r'^\\s*\\|(?:\\s*:?-+:?\\s*\\|)+')\n", "    \n", "    tables = []\n", "    in_table = False\n", "    current_table_lines = []\n", "    \n", "    # This variable will hold the line that triggered table detection.\n", "    # It helps to capture the header.\n", "    header_candidate = None\n", "\n", "    for line in lines:\n", "        is_table_line = '|' in line\n", "        is_header_separator = bool(header_separator_regex.match(line))\n", "\n", "        if is_header_separator:\n", "            # The current line is a separator. If we have a header candidate,\n", "            # it means we've found a valid table.\n", "            if header_candidate:\n", "                current_table_lines = [header_candidate, line]\n", "                in_table = True\n", "            header_candidate = None # Reset the candidate\n", "            continue\n", "\n", "        if in_table:\n", "            # We are already inside a table.\n", "            if is_table_line:\n", "                # Add the current line to the table body.\n", "                current_table_lines.append(line)\n", "            else:\n", "                # The table has ended.\n", "                tables.append(\"\\n\".join(current_table_lines))\n", "                in_table = False\n", "                current_table_lines = []\n", "        \n", "        # Reset if the line is not a potential part of a table.\n", "        header_candidate = line if is_table_line else None\n", "\n", "    # After the loop, save any table that was at the end of the file.\n", "    if in_table and current_table_lines:\n", "        tables.append(\"\\n\".join(current_table_lines))\n", "        \n", "    return tables\n", "\n", "def extract_tables_from_markdown_file(file_path: str) -> list[str]:\n", "    \"\"\"\n", "    Reads a Markdown file and extracts all tables from it.\n", "\n", "    Args:\n", "        file_path: The full path to the .md file.\n", "\n", "    Returns:\n", "        A list of strings, where each string is a complete Markdown table.\n", "        Returns an empty list if the file is not found or an error occurs.\n", "    \"\"\"\n", "    if not os.path.exists(file_path):\n", "        print(f\"Error: File not found at '{file_path}'\")\n", "        return []\n", "    \n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            content = f.read()\n", "        \n", "        return find_markdown_tables(content)\n", "        \n", "    except Exception as e:\n", "        print(f\"An error occurred while reading the file: {e}\")\n", "        return []\n", "\n", "# --- Example Usage ---\n", "dummy_file_path = 'data/markdowns/1_MEDICARD_image_1_0_extraction.md'\n", "\n", "# 2. Call the function with the path to your Markdown file.\n", "print(f\"Extracting tables from: {dummy_file_path}\\n\")\n", "extracted_tables = extract_tables_from_markdown_file(dummy_file_path)\n", "\n", "# 3. Print the results.\n", "if extracted_tables:\n", "    for i, table in enumerate(extracted_tables):\n", "        print(f\"--- Found Table {i+1} ---\")\n", "        print(table)\n", "        print()\n", "else:\n", "    print(\"No tables were found in the document.\")"]}, {"cell_type": "code", "execution_count": null, "id": "70093036", "metadata": {}, "outputs": [], "source": ["import pymupdf4llm\n", "text = pymupdf4llm.to_markdown(\n", "    \"data/1_MEDICARD.pdf\",\n", "    page_chunks=True,\n", "    show_progress=True,\n", "    write_images=True,\n", "    image_path=\"data/images_pdf/\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "655e45f2", "metadata": {}, "outputs": [], "source": ["text"]}, {"cell_type": "code", "execution_count": null, "id": "3a9125e8", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pymupdf4llm\n", "import base64\n", "from pathlib import Path\n", "from openai import AzureOpenAI\n", "import concurrent.futures\n", "\n", "# --- Prerequisite: Function to Call Vision LLM ---\n", "# This function is the same as before. It takes an image path and returns text.\n", "def extract_content_from_image(image_path: str, client: AzureOpenAI, model_name: str) -> str:\n", "    \"\"\"Extracts content from an image file using a vision model.\"\"\"\n", "    if not os.path.exists(image_path):\n", "        return f\"[Error: Image file not found at {image_path}]\"\n", "    \n", "    try:\n", "        with open(image_path, \"rb\") as image_file:\n", "            base64_image = base64.b64encode(image_file.read()).decode(\"utf-8\")\n", "\n", "        prompt_text = \"\"\"\n", "        You are an expert AI assistant specializing in accurate data extraction from documents.\n", "        Your task is to transcribe all content from the provided image. This includes paragraphs, lists, and tables.\n", "\n", "        GUIDELINES:\n", "        1. Transcribe all text verbatim, from top to bottom.\n", "        2. When you identify a table, format it as a Markdown table.\n", "        3. For all non-table text, transcribe it as plain text, maintaining original formatting.\n", "        4. Do not summarize or omit any information.\n", "        \"\"\"\n", "        response = client.chat.completions.create(\n", "            model=model_name,\n", "            messages=[\n", "                {\"role\": \"user\", \"content\": [\n", "                    {\"type\": \"text\", \"text\": prompt_text},\n", "                    {\"type\": \"image_url\", \"image_url\": {\"url\": f\"data:image/jpeg;base64,{base64_image}\"}},\n", "                ]}\n", "            ]\n", "        )\n", "        return response.choices[0].message.content\n", "    except Exception as e:\n", "        print(f\"  - Error during LLM call for {os.path.basename(image_path)}: {e}\")\n", "        return f\"[Error extracting content from image: {os.path.basename(image_path)}]\"\n", "\n", "# --- Main Pipeline Function (REVISED) ---\n", "\n", "def build_document_from_pdf(pdf_path: str, output_dir: str, client: AzureOpenAI, model_name: str) -> list:\n", "    \"\"\"\n", "    Builds a detailed document, handling pages with mixed text and images.\n", "    \"\"\"\n", "    output_path = Path(output_dir)\n", "    image_output_path = output_path / \"images\"\n", "    image_output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    print(\"Step 1: Parsing PDF with pymupdf4llm...\")\n", "    parsed_pages = pymupdf4llm.to_markdown(\n", "        pdf_path, page_chunks=True, write_images=True, image_path=str(image_output_path)\n", "    )\n", "    print(f\"Initial parsing complete. Found {len(parsed_pages)} pages.\")\n", "\n", "    # 1. NEW: Find all unique image links across all pages\n", "    unique_image_paths = set()\n", "    image_regex = re.compile(r'!\\[\\]\\((.*?)\\)') # Finds all markdown image links\n", "\n", "    for page in parsed_pages:\n", "        found_images = image_regex.findall(page.get(\"text\", \"\"))\n", "        for img_path in found_images:\n", "            unique_image_paths.add(img_path)\n", "\n", "    # 2. Process all unique images concurrently\n", "    llm_results = {} # Store results as { \"image_path\": \"transcribed_text\" }\n", "    if unique_image_paths:\n", "        print(f\"\\nStep 2: Found {len(unique_image_paths)} unique images. Transcribing with Vision LLM...\")\n", "        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:\n", "            future_to_path = {\n", "                executor.submit(extract_content_from_image, path, client, model_name): path\n", "                for path in unique_image_paths\n", "            }\n", "            for future in concurrent.futures.as_completed(future_to_path):\n", "                path = future_to_path[future]\n", "                try:\n", "                    content = future.result(timeout=30)\n", "                    llm_results[path] = content\n", "                    print(f\"- Transcription received for {os.path.basename(path)}\")\n", "                except Exception as e:\n", "                    llm_results[path] = f\"[Error processing {os.path.basename(path)}: {e}]\"\n", "    else:\n", "        print(\"\\nStep 2: No images found to process.\")\n", "\n", "    # 3. NEW: Replace each image link with its transcription in the original text\n", "    print(\"\\nStep 3: Merging LLM results into the final document structure...\")\n", "    for page in parsed_pages:\n", "        original_text = page.get(\"text\", \"\")\n", "        # Find all images that might be on this page\n", "        images_on_page = image_regex.findall(original_text)\n", "        \n", "        # Replace each one with its corresponding LLM result\n", "        for img_path in images_on_page:\n", "            if img_path in llm_results:\n", "                transcription = llm_results[img_path]\n", "                # Construct the markdown link to replace it accurately\n", "                markdown_link_to_replace = f\"![]({img_path})\"\n", "                page[\"text\"] = page[\"text\"].replace(markdown_link_to_replace, transcription)\n", "        \n", "        # Add metadata to indicate the source of the content\n", "        if images_on_page:\n", "            page['metadata']['content_source'] = 'Text + VisionLLM'\n", "\n", "    print(\"Pipeline finished successfully.\")\n", "    return parsed_pages\n", "\n", "\n", "# --- Example Usage ---\n", "\n", "# 1. CONFIGURE YOUR CLIENT \n", "# 1. CONFIGURE YOUR CLIENT \n", "client = AzureOpenAI(\n", "    api_version=azure_api_version,\n", "    azure_endpoint=azure_endpoint,\n", "    api_key=azure_api_key,\n", ")\n", "# This is the name of your model deployment in Azure AI Studio\n", "AZURE_DEPLOYMENT_NAME = \"gpt-4o-mini\"\n", "\n", "# 2. DEFINE YOUR PATHS\n", "# pdf_to_process = \"data/1_MEDICARD.pdf\"\n", "# pdf_to_process = \"data/2_VL POLICY.pdf\" \n", "# pdf_to_process = \"data/Activ_Health_Platinum_Essential_Policy_Wording_ca300bebe2.pdf\" \n", "pdf_to_process = \"data/Care_Heart_Policy_Wording_fb66f443af.pdf\" \n", "output_directory = 'output_data'        # All results will be saved here\n", "\n", "# 3. RUN THE FULL PIPELINE\n", "final_document_data = build_document_from_pdf(\n", "    pdf_path=pdf_to_process,\n", "    output_dir=output_directory,\n", "    client=client,\n", "    model_name=AZURE_DEPLOYMENT_NAME\n", ")\n", "\n", "# 4. Save the final result\n", "report_filename = f\"{Path(pdf_to_process).stem}_extracted.json\"\n", "report_filepath = Path(output_directory) / report_filename\n", "with open(report_filepath, 'w', encoding='utf-8') as f:\n", "    json.dump(final_document_data, f, indent=4)\n", "\n", "print(f\"\\nFinal structured data saved to: {report_filepath}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1f010e57", "metadata": {}, "outputs": [], "source": ["import json\n", "from pathlib import Path\n", "from IPython.display import display, Markdown\n", "\n", "def display_report_from_json(file_path: str):\n", "    \"\"\"\n", "    Reads a JSON report file and displays the content of each page as Markdown.\n", "\n", "    Args:\n", "        file_path: The path to the JSON report file.\n", "    \"\"\"\n", "    report_path = Path(file_path)\n", "    if not report_path.exists():\n", "        print(f\"Error: Report file not found at '{file_path}'\")\n", "        return\n", "\n", "    print(f\"Reading report from: {report_path}\\n\")\n", "    \n", "    try:\n", "        with open(report_path, 'r', encoding='utf-8') as f:\n", "            # Load the entire list of page data from the JSON file\n", "            all_pages_data = json.load(f)\n", "\n", "        # Loop through each page's data in the list\n", "        for page_data in all_pages_data:\n", "            metadata = page_data.get(\"metadata\", {})\n", "            page_num = metadata.get(\"page\", \"N/A\")\n", "            \n", "            # Get the markdown content to display\n", "            content = page_data.get(\"text\", \"No content found for this page.\")\n", "            \n", "            # Print a separator and header for clarity\n", "            print(\"---\" * 20)\n", "            print(f\"## Page {page_num}\")\n", "            print(\"---\" * 20)\n", "            \n", "            # Display the content as rendered Markdown\n", "            display(Markdown(content))\n", "\n", "    except json.JSONDecodeError:\n", "        print(f\"Error: Could not decode JSON from '{file_path}'. The file might be corrupted or empty.\")\n", "    except Exception as e:\n", "        print(f\"An unexpected error occurred: {e}\")\n", "\n", "\n", "# --- HOW TO USE ---\n", "\n", "# 1. DEFINE THE PATH to the JSON report created by the previous script.\n", "#    Make sure this path is correct.\n", "output_directory = 'output_data'\n", "# pdf_filename = '1_MEDICARD' # The base name of your PDF\n", "pdf_filename = '2_VL POLICY' # The base name of your PDF\n", "report_filepath = Path(output_directory) / f\"{pdf_filename}_extracted.json\"\n", "\n", "# 2. CALL THE FUNCTION to display the report\n", "display_report_from_json(str(report_filepath))"]}, {"cell_type": "code", "execution_count": null, "id": "e3ecee70", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from IPython.display import display, Markdown\n", "from pymupdf_extractor import convert_pymupdf\n", "from save_markdowns import save_markdown"]}, {"cell_type": "code", "execution_count": null, "id": "a6423a6d", "metadata": {}, "outputs": [], "source": ["# path = \"examples/PDF_Parsing_Analysis.pdf\"\n", "path = \"data/1_MEDICARD.pdf\"\n", "filename = Path(path).stem\n", "# PyMuPdf4llm parser\n", "text,debug_image_paths = convert_pymupdf(path,filename)\n", "# save_markdown(text,f\"{filename}_extracted_text_pymudf.md\")\n", "display(Markdown(text))"]}, {"cell_type": "code", "execution_count": null, "id": "1fdaec63", "metadata": {}, "outputs": [], "source": ["# path = \"examples/PDF_Parsing_Analysis.pdf\"\n", "path = \"data/2_VL POLICY.pdf\"\n", "filename = Path(path).stem\n"]}, {"cell_type": "code", "execution_count": null, "id": "ea3ad89f", "metadata": {}, "outputs": [], "source": ["# PyMuPdf4llm parser\n", "text,debug_image_paths = convert_pymupdf(path,filename)\n", "# save_markdown(text,f\"{filename}_extracted_text_pymudf.md\")\n", "display(Markdown(text))"]}, {"cell_type": "code", "execution_count": null, "id": "335e71ad", "metadata": {}, "outputs": [], "source": ["# path = \"examples/PDF_Parsing_Analysis.pdf\"\n", "path = \"data/Activ_Health_Platinum_Essential_Policy_Wording_ca300bebe2.pdf\"\n", "filename = Path(path).stem\n", "# PyMuPdf4llm parser\n", "text,debug_image_paths = convert_pymupdf(path,filename)\n", "# save_markdown(text,f\"{filename}_extracted_text_pymudf.md\")\n", "display(Markdown(text))"]}, {"cell_type": "code", "execution_count": null, "id": "b813f3f6", "metadata": {}, "outputs": [], "source": ["# path = \"examples/PDF_Parsing_Analysis.pdf\"\n", "path = \"data/Care_Heart_Policy_Wording_fb66f443af.pdf\"\n", "filename = Path(path).stem\n", "# PyMuPdf4llm parser\n", "text,debug_image_paths = convert_pymupdf(path,filename)\n", "# save_markdown(text,f\"{filename}_extracted_text_pymudf.md\")\n", "display(Markdown(text))"]}, {"cell_type": "code", "execution_count": null, "id": "2c1a1552", "metadata": {}, "outputs": [], "source": ["import uuid\n", "import chromadb\n", "from typing import Tuple, List\n", "import re\n", "\n", "# def find_markdown_tables(markdown_text: str) -> Tuple[List[str], str]:\n", "#     \"\"\"Finds all tables in markdown text and returns them, along with the text without tables.\"\"\"\n", "#     # This regex finds table structures based on the header separator line\n", "#     # It now correctly captures the full table block\n", "#     table_pattern = re.compile(r'(?:^\\|.*(?:(?:\\r\\n?|\\n)(?:\\|.*))*)')\n", "#     tables = table_pattern.findall(markdown_text)\n", "    \n", "#     # Remove found tables from the original text\n", "#     text_without_tables = markdown_text\n", "#     for table in tables:\n", "#         text_without_tables = text_without_tables.replace(table, \"\")\n", "        \n", "#     return tables, text_without_tables.strip()\n", "\n", "def find_markdown_tables_final_revised(markdown_text: str) -> list[str]:\n", "    \"\"\"\n", "    Finds and extracts all Markdown tables, correctly handling tables\n", "    with empty or complex headers.\n", "    \"\"\"\n", "    lines = markdown_text.strip().split('\\n')\n", "    tables = []\n", "    \n", "    # Regex to find the table header separator line\n", "    header_separator_regex = re.compile(r'^\\s*\\|(?:\\s*:?-+:?\\s*\\|)+')\n", "    \n", "    # --- First Pass: Find all separator lines ---\n", "    separator_indices = [i for i, line in enumerate(lines) if header_separator_regex.match(line)]\n", "    \n", "    processed_up_to = -1 # Keep track of where we've processed to avoid duplicates\n", "\n", "    # --- Second Pass: Extract the full table around each separator ---\n", "    for sep_index in separator_indices:\n", "        if sep_index <= processed_up_to or sep_index == 0:\n", "            continue\n", "\n", "        # The table header is the line just before the separator\n", "        start_index = sep_index - 1\n", "        \n", "        # The table body starts on the line just after the separator\n", "        end_index = sep_index + 1\n", "        \n", "        # Scan forward to find the end of the table body\n", "        while end_index < len(lines) and '|' in lines[end_index]:\n", "            end_index += 1\n", "            \n", "        # Slice the lines to get the complete table\n", "        table_lines = lines[start_index:end_index]\n", "        tables.append(\"\\n\".join(table_lines))\n", "        \n", "        # Update the index to prevent re-processing this table\n", "        processed_up_to = end_index - 1\n", "\n", "    # Remove found tables from the original text\n", "    text_without_tables = markdown_text\n", "    for table in tables:\n", "        text_without_tables = text_without_tables.replace(table, \"\")\n", "        \n", "    return tables, text_without_tables.strip()\n", "\n", "\n", "def summarize_table_with_llm(table_markdown: str, client: AzureOpenAI, model_name: str) -> str:\n", "    \"\"\"Uses an LLM to generate a summary for a given Markdown table.\"\"\"\n", "    prompt = f\"\"\"\n", "    Summarize the following Markdown table concisely. Describe its main purpose, its columns,\n", "    and the key categories of data it contains. This summary will be used for vector search.\n", "\n", "    TABLE:\n", "    {table_markdown}\n", "    \"\"\"\n", "    try:\n", "        response = client.chat.completions.create(\n", "            model=model_name,\n", "            messages=[{\"role\": \"user\", \"content\": prompt}],\n", "            max_tokens=512,\n", "            temperature=0.2,\n", "        )\n", "        return response.choices[0].message.content\n", "    except Exception as e:\n", "        print(f\"  - Error summarizing table: {e}\")\n", "        return \"Summary of a data table.\" # Fallback summary\n", "\n", "\n", "def index_from_json_for_rbac(\n", "    json_file_path: str,\n", "    company_id: str,\n", "    collection: chromadb.Collection,\n", "    client: AzureOpenAI,\n", "    model_name: str,\n", "    overflow_tables: dict = None\n", "):\n", "    \"\"\"\n", "    Pipeline 2: Reads an extracted JSON file, chunks the data, and indexes it into ChromaDB for RBAC.\n", "    \"\"\"\n", "    print(f\"\\n--- Starting Indexing Pipeline for: {json_file_path} ---\")\n", "    \n", "    # 1. Load the pre-processed data from the JSON file\n", "    with open(json_file_path, 'r', encoding='utf-8') as f:\n", "        all_pages_data = json.load(f)\n", "\n", "    # Create a dictionary mapping page number to its full markdown content\n", "    page_contents = {page['metadata']['page']: page['text'] for page in all_pages_data}\n", "    \n", "    # 2. <PERSON><PERSON> multi-page table content before processing\n", "    processed_pages = {}\n", "    pages_to_skip = set()\n", "    if overflow_tables:\n", "        for start_page, all_pages in overflow_tables.items():\n", "            full_content = \"\\n\\n\".join([page_contents.get(p, \"\") for p in all_pages])\n", "            processed_pages[str(all_pages)] = full_content\n", "            pages_to_skip.update(all_pages)\n", "    print(processed_pages)\n", "    for page_num, content in page_contents.items():\n", "        if page_num not in pages_to_skip:\n", "            processed_pages[str(page_num)] = content\n", "    \n", "    # Prepare lists for batch insertion\n", "    chunks_to_embed, metadatas, ids = [], [], []\n", "\n", "    # 3. Apply hybrid chunking strategy\n", "    print(\"Applying hybrid chunking strategy...\")\n", "    for page_key, content in processed_pages.items():\n", "        tables, text_only = find_markdown_tables_final_revised(content) # Assumes find_markdown_tables is defined\n", "        # print(tables)\n", "        # for table in tables:\n", "            # display(Markdown(table))\n", "        display(Markdown(text_only))\n", "        # print(text_only)\n", "        # Process tables: summarize for embedding, store raw table in metadata\n", "    #     for table in tables:\n", "    #         summary = summarize_table_with_llm(table, client, model_name) # Assumes summarize_table_with_llm is defined\n", "    #         chunks_to_embed.append(summary)\n", "    #         metadatas.append({\n", "    #             \"company_id\": company_id,\n", "    #             \"source_file\": Path(json_file_path).stem.replace(\"_extracted\", \".pdf\"),\n", "    #             \"page_number\": page_key,\n", "    #             \"content_type\": \"table_summary\",\n", "    #             \"raw_table_markdown\": table\n", "    #         })\n", "    #         ids.append(str(uuid.uuid4()))\n", "\n", "    #     # Process text: split into paragraphs\n", "    #     text_chunks = [p.strip() for p in text_only.split('\\n\\n') if p.strip()]\n", "    #     for chunk in text_chunks:\n", "    #         chunks_to_embed.append(chunk)\n", "    #         metadatas.append({\n", "    #             \"company_id\": company_id,\n", "    #             \"source_file\": Path(json_file_path).stem.replace(\"_extracted\", \".pdf\"),\n", "    #             \"page_number\": page_key,\n", "    #             \"content_type\": \"text\"\n", "    #         })\n", "    #         ids.append(str(uuid.uuid4()))\n", "\n", "    # # 4. Add all chunks to ChromaDB\n", "    # if chunks_to_embed:\n", "    #     print(f\"Adding {len(chunks_to_embed)} chunks to ChromaDB...\")\n", "    #     collection.add(documents=chunks_to_embed, metadatas=metadatas, ids=ids)\n", "        print(\"Indexing complete.\")\n", "\n", "# NOTE: You will need to define `find_markdown_tables` and `summarize_table_with_llm` as provided\n", "# in previous answers for the indexing pipeline to be complete.\n", "\n", "# --- Example of running both pipelines in sequence ---\n", "\n", "if __name__ == \"__main__\":\n", "    # --- Shared Setup ---\n", "    # 1. CONFIGURE YOUR CLIENT \n", "    # --- Run <PERSON> 2: Indexing ---\n", "    CHROMA_PATH = \"rbac_store\"\n", "    COLLECTION_NAME = \"insurance_docs\"\n", "    chroma_client = chromadb.PersistentClient(path=CHROMA_PATH)\n", "    collection = chroma_client.get_or_create_collection(name=COLLECTION_NAME)\n", "\n", "    company_of_document = \"company_a\"\n", "    multi_page_tables = { 1: [1,2, 3, 4],7: [7, 8],8: [8, 9] }\n", "\n", "    index_from_json_for_rbac(\n", "        json_file_path='output_data/1_MEDICARD_final_report.json',\n", "        company_id=company_of_document,\n", "        collection=collection,\n", "        client=client,\n", "        model_name=AZURE_DEPLOYMENT_NAME,\n", "        overflow_tables=multi_page_tables\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "0306a335", "metadata": {}, "outputs": [], "source": ["import os\n", "import uuid\n", "import json\n", "import chromadb\n", "from pathlib import Path\n", "from langchain_openai import AzureOpenAIEmbeddings # Correct import for embeddings\n", "\n", "def index_pages_from_json_for_rbac(\n", "    json_file_path: str,\n", "    company_id: str,\n", "    collection: chromadb.Collection,\n", "    overflow_tables: dict = None\n", "):\n", "    \"\"\"\n", "    Reads an extracted JSON file, treats each page as a single chunk,\n", "    generates embeddings, and indexes it into ChromaDB with company_id for RBAC.\n", "    \"\"\"\n", "    print(f\"\\n--- Starting Page-Level Indexing for: {json_file_path} ---\")\n", "\n", "    # --- 1. Initialize the Embedding Model ---\n", "    # The model is created here using your Azure credentials from environment variables.\n", "    print(\"Initializing embedding model...\")\n", "    embeddings_model = AzureOpenAIEmbeddings(\n", "        openai_api_key=azure_api_key,\n", "        openai_api_version=azure_api_version,\n", "        azure_endpoint=azure_endpoint,\n", "        model=azure_embedding_model\n", "    )\n", "\n", "    # 2. Load the pre-processed data from the JSON file\n", "    with open(json_file_path, 'r', encoding='utf-8') as f:\n", "        all_pages_data = json.load(f)\n", "\n", "    page_contents = {page['metadata']['page']: page['text'] for page in all_pages_data}\n", "\n", "    # Data structures for batch insertion\n", "    documents_to_index = []\n", "    metadatas = []\n", "    ids = []\n", "    pages_processed_in_groups = set()\n", "\n", "    # 3. <PERSON><PERSON> and stitch all multi-page groups\n", "    if overflow_tables:\n", "        for start_page, all_pages in overflow_tables.items():\n", "            page_key = str(all_pages)\n", "            full_content = \"\\n\\n--- PAGE BREAK ---\\n\\n\".join([page_contents.get(p, \"\") for p in all_pages])\n", "            documents_to_index.append(full_content)\n", "            metadatas.append({\n", "                \"company_id\": company_id,\n", "                \"source_file\": Path(json_file_path).stem.replace(\"_extracted\", \".pdf\"),\n", "                \"page_number\": page_key,\n", "            })\n", "            ids.append(str(uuid.uuid4()))\n", "            pages_processed_in_groups.update(all_pages)\n", "\n", "    # 4. Process all remaining single pages\n", "    for page_num, content in page_contents.items():\n", "        if page_num in pages_processed_in_groups:\n", "            continue\n", "        documents_to_index.append(content)\n", "        metadatas.append({\n", "            \"company_id\": company_id,\n", "            \"source_file\": Path(json_file_path).stem.replace(\"_extracted\", \".pdf\"),\n", "            \"page_number\": str(page_num),\n", "        })\n", "        ids.append(str(uuid.uuid4()))\n", "\n", "    # 5. Generate Embeddings for all documents\n", "    if documents_to_index:\n", "        print(f\"\\nGenerating embeddings for {len(documents_to_index)} page-level chunks...\")\n", "        # This is where the embedding model is used to convert text to vectors.\n", "        embeddings = embeddings_model.embed_documents(documents_to_index)\n", "        print(\"Embeddings generated successfully.\")\n", "\n", "        # 6. Add all data (including the pre-generated embeddings) to ChromaDB\n", "        print(f\"Adding chunks and embeddings to ChromaDB...\")\n", "        collection.add(\n", "            embeddings=embeddings, # Pass the generated vectors\n", "            documents=documents_to_index,\n", "            metadatas=metadatas,\n", "            ids=ids\n", "        )\n", "        print(\"Page-level indexing complete.\")\n", "    else:\n", "        print(\"No content found to index.\")\n", "\n", "# --- Example Usage ---\n", "\n", "if __name__ == \"__main__\":\n", "    # Assumes you have an \"_extracted.json\" file ready from Pipeline 1.\n", "    CHROMA_PATH = \"rag_store\"\n", "    COLLECTION_NAME = \"acen_docs\"\n", "    \n", "    chroma_client = chromadb.PersistentClient(path=CHROMA_PATH)\n", "    collection = chroma_client.get_or_create_collection(name=COLLECTION_NAME)\n", "    json_to_process = \"output_data/1_MEDICARD_extracted.json\"\n", "    company_of_document = \"ACEN1234\"\n", "    # json_to_process = \"output_data/2_VL POLICY_extracted.json\"\n", "    # company_of_document = \"ACEN1234\"\n", "    # json_to_process = \"output_data/Activ_Health_Platinum_Essential_Policy_Wording_ca300bebe2_extracted.json\"\n", "    # company_of_document = \"ABC1234\"\n", "    # json_to_process = \"output_data/Care_Heart_Policy_Wording_fb66f443af_extracted.json\"\n", "    # company_of_document = \"CHP1234\"\n", "    \n", "    multi_page_tables = { 1: [1,2, 3, 4], 7: [7, 8], 8: [8, 9],14:[14,15] }\n", "    # multi_page_tables = {}\n", "    \n", "    if os.path.exists(json_to_process):\n", "        index_pages_from_json_for_rbac(\n", "            json_file_path=json_to_process,\n", "            company_id=company_of_document,\n", "            collection=collection,\n", "            overflow_tables=multi_page_tables\n", "        )\n", "    else:\n", "        print(f\"Error: JSON file not found at '{json_to_process}'.\")"]}, {"cell_type": "code", "execution_count": null, "id": "d68426ed", "metadata": {}, "outputs": [], "source": ["import os\n", "import chromadb\n", "from chromadb.config import Settings\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "from typing import List, Dict, Any, Optional\n", "\n", "# This function is fine as is, but updated to disable telemetry.\n", "def load_chroma_db(collection_name: str, persist_directory: str = \"rag_store\") -> chromadb.Collection:\n", "    \"\"\"\n", "    Loads a ChromaDB vector store from disk.\n", "\n", "    Args:\n", "        collection_name: The name of the collection to load.\n", "        persist_directory: Directory where the database is stored.\n", "\n", "    Returns:\n", "        A ChromaDB collection object.\n", "    \"\"\"\n", "    try:\n", "        # Initialize Chroma client with telemetry disabled\n", "        client = chromadb.PersistentClient(\n", "            path=persist_directory\n", "        )\n", "        # Retrieve the collection\n", "        collection = client.get_collection(name=collection_name)\n", "        return collection\n", "    except Exception as e:\n", "        raise RuntimeError(f\"Error loading ChromaDB: {str(e)}\")\n", "\n", "def query_top_chunks(\n", "    query_text: str,\n", "    collection_name: str,\n", "    persist_directory: str = \"rag_store\",\n", "    top_k: int = 5,\n", "    company_id: Optional[str] = None \n", ") -> Dict[str, List[List[Any]]]:\n", "    \"\"\"\n", "    Queries a ChromaDB collection, filters by company_id if provided,\n", "    cleans for duplicates, and returns the top k results.\n", "\n", "    Args:\n", "        query_text: The query to search for.\n", "        collection_name: The name of the collection.\n", "        persist_directory: Directory where the database is stored.\n", "        top_k: The final number of unique results to return.\n", "        company_id: Optional company ID to filter results for a specific tenant.\n", "\n", "    Returns:\n", "        A cleaned dictionary of top-k documents and their metadata.\n", "    \"\"\"\n", "    try:\n", "        collection = load_chroma_db(collection_name, persist_directory)\n", "\n", "        # Initialize the embedding model\n", "        embeddings_model = AzureOpenAIEmbeddings(\n", "            openai_api_key=azure_api_key,\n", "            openai_api_version=azure_api_version,\n", "            azure_endpoint=azure_endpoint,\n", "            model=azure_embedding_model\n", "        )\n", "\n", "        # Generate embedding for the query using the correct method\n", "        query_embedding = embeddings_model.embed_query(query_text)\n", "\n", "        # Construct the 'where' filter for role-based access\n", "        where_filter = {\"company_id\": company_id} if company_id else None\n", "        \n", "        # Query the collection\n", "        results = collection.query(\n", "            query_embeddings=[query_embedding], # Pass the single embedding as a list\n", "            n_results=top_k + 10,  # Fetch extra results to account for duplicates\n", "            where=where_filter    # Apply the company filter here\n", "        )\n", "\n", "        # --- Cleaning and De-duplication Logic ---\n", "        cleaned_results = {}\n", "        unique_documents = []\n", "        unique_metadatas = []\n", "        seen_docs = set() # Use a set for efficient checking\n", "\n", "        if results and results[\"documents\"]:\n", "            for doc, meta in zip(results[\"documents\"][0], results[\"metadatas\"][0]):\n", "                if doc not in seen_docs:\n", "                    seen_docs.add(doc)\n", "                    unique_documents.append(doc)\n", "                    unique_metadatas.append(meta)\n", "\n", "        # Slice to the desired top_k unique results\n", "        cleaned_results['documents'] = [unique_documents[:top_k]]\n", "        cleaned_results['metadatas'] = [unique_metadatas[:top_k]]\n", "\n", "        return cleaned_results\n", "\n", "    except Exception as e:\n", "        print(f\"Error querying chunks: {str(e)}\")\n", "        # Return a dictionary with empty lists in case of an error\n", "        return {'documents': [[]], 'metadatas': [[]]}\n", "\n", "\n", "# --- Example Usage ---\n", "if __name__ == \"__main__\":\n", "    # Ensure your environment variables for Azure OpenAI are set\n", "    \n", "    COLLECTION_NAME = \"acen_docs\"\n", "    \n", "    # -- Scenario 1: Query as a Regular User for \"company_a\" --\n", "    print(\"--- Querying as a Regular User (company_a) ---\")\n", "    user_results = query_top_chunks(\n", "        query_text=\"What is the deferment of leave credits?\",\n", "        collection_name=COLLECTION_NAME,\n", "        company_id=\"ACEN1234\", # This will filter the results\n", "        top_k=3\n", "    )\n", "    print(\"User Results (should only contain company_a docs):\")\n", "    import pprint\n", "    pprint.pprint(user_results)\n", "    \n", "    print(\"\\n\" + \"=\"*50 + \"\\n\")\n", "    \n", "    # # -- Scenario 2: Query as an Admin --\n", "    # print(\"--- Querying as an Admin (no company filter) ---\")\n", "    # admin_results = query_top_chunks(\n", "    #     query_text=\"What is the limit for organ transplants?\",\n", "    #     collection_name=COLLECTION_NAME,\n", "    #     company_id=None, # Passing None means no filter is applied\n", "    #     top_k=3\n", "    # )\n", "    # print(\"Admin Results (can contain docs from any company):\")\n", "    # pprint.pprint(admin_results)"]}, {"cell_type": "code", "execution_count": null, "id": "f2c00c3c", "metadata": {}, "outputs": [], "source": ["import os\n", "import chromadb\n", "from chromadb.config import Settings\n", "from langchain_openai import AzureOpenAIEmbeddings\n", "import pprint # For pretty printing results\n", "\n", "# --- 1. Setup: Initialize Clients and Models ---\n", "\n", "# Initialize ChromaDB client with telemetry disabled\n", "CHROMA_PATH = \"rag_store\"\n", "COLLECTION_NAME = \"acen_docs\" # Use the same collection name\n", "\n", "print(\"Initializing ChromaDB client...\")\n", "client = chromadb.PersistentClient(\n", "    path=CHROMA_PATH,\n", "    # settings=Settings(anonymized_telemetry=False)\n", ")\n", "\n", "# Get the existing collection\n", "try:\n", "    collection = client.get_collection(name=COLLECTION_NAME)\n", "    print(f\"Successfully connected to collection '{COLLECTION_NAME}'.\")\n", "except ValueError:\n", "    print(f\"Error: Collection '{COLLECTION_NAME}' not found. Please run the indexing script first.\")\n", "    exit()\n", "\n", "# Initialize the same embedding model used for indexing\n", "print(\"Initializing embedding model...\")\n", "embeddings_model = AzureOpenAIEmbeddings(\n", "    openai_api_key=azure_api_key,\n", "    openai_api_version=azure_api_version,\n", "    azure_endpoint=azure_endpoint,\n", "    model=azure_embedding_model\n", ")\n", "def print_results(results: dict):\n", "    \"\"\"Helper function to cleanly print query results.\"\"\"\n", "    if not results or not results.get('ids')[0]:\n", "        print(\"No results found.\")\n", "        return\n", "        \n", "    for i, doc in enumerate(results['documents'][0]):\n", "        print(f\"\\n--- Result {i+1} ---\")\n", "        print(f\"Distance: {results['distances'][0][i]:.4f}\")\n", "        print(\"Metadata:\")\n", "        pprint.pprint(results['metadatas'][0][i])\n", "        # print(\"\\nContent Preview:\")\n", "        # print(f\"{doc[:500]}...\") # Print a preview of the content\n", "    print(\"-\" * 20)\n", "\n", "# --- 2. Querying as a Regular User 👤 ---\n", "# The application knows the user belongs to 'company_a'.\n", "# A 'where' filter is applied to the query.\n", "\n", "print(\"\\n\\n--- Scenario 1: Querying as a Regular User from 'company_a' ---\")\n", "user_query = \"What is the MAXIMUM BENIFIT LIMIT for a Class 1 employee?; How much of <PERSON><PERSON>'s fee is covered for an AVP?\"\n", "# user_query = \"What is the deferment of leave credits?\"\n", "# user_query = \"what is my MBL for basic medical expenses\"\n", "user_company_id = \"ACEN1234\"\n", "\n", "# The 'where' filter restricts the search to a specific company\n", "query_embedding =  embeddings_model.embed_documents([user_query])\n", "user_results = collection.query(\n", "    query_embeddings=query_embedding,\n", "    n_results=5,\n", "    # where={\"company_id\": user_company_id} # <-- SECURITY FILTER IS APPLIED HERE\n", ")\n", "\n", "print(f\"Query: '{user_query}'\")\n", "print(f\"Filter Applied: {{'company_id': '{user_company_id}'}}\")\n", "print_results(user_results)"]}, {"cell_type": "code", "execution_count": null, "id": "d09c5f12", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c2a1d504", "metadata": {}, "outputs": [], "source": ["user_results"]}, {"cell_type": "code", "execution_count": null, "id": "f82a95ca", "metadata": {}, "outputs": [], "source": ["import chromadb\n", "\n", "client = chromadb.PersistentClient(\n", "    path='rag_store',\n", "    # settings=Settings(anonymized_telemetry=False)\n", ")\n", "collection = client.get_collection(\"acen_docs\")\n", "\n", "# 1. Get IDs matching a where condition\n", "results = collection.get(\n", "    where={\n", "        \"$and\": [\n", "            {\"company_id\": \"ACEN1234\"},\n", "            {\"source_file\": \"1_MEDICARD.pdf\"}\n", "        ]\n", "    }\n", ")\n", "ids_to_delete = results['ids']\n", "\n", "# # 2. Delete those documents\n", "# if ids_to_delete:\n", "#     collection.delete(ids=ids_to_delete)"]}, {"cell_type": "code", "execution_count": null, "id": "4f250fcd", "metadata": {}, "outputs": [], "source": ["results"]}, {"cell_type": "code", "execution_count": null, "id": "b7a30856", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import uuid\n", "\n", "# Define the data, generating random user IDs\n", "data = {\n", "    # Generate a unique 8-character ID for each user\n", "    'id': [str(uuid.uuid4())[:8] for _ in range(4)],\n", "    'name': ['<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],\n", "    'role': ['user', 'user', 'user', 'user'],\n", "    'designation': ['staff', 'associate', 'AVP', 'Sr.associate'],\n", "    'class': [1, 1, 3, 2],\n", "    'company_id': ['ACEN1234', 'ACEN1234', 'ABC1234', 'AHP1234']\n", "}\n", "\n", "# Create the pandas DataFrame\n", "df = pd.DataFrame(data)\n", "\n", "# Display the DataFrame\n", "df"]}, {"cell_type": "code", "execution_count": null, "id": "dd28ddad", "metadata": {}, "outputs": [], "source": ["import re\n", "\n", "def expand_abbreviations(text):\n", "    abbr_map = {\n", "        \"MBL\": \"Maximum Benefit Limit\",\n", "        \"ABL\": \"Annual Benefit Limit\",\n", "    }\n", "    # Regex pattern: match the abbreviations as full words (word boundaries)\n", "    pattern = re.compile(r'\\b(' + '|'.join(abbr_map.keys()) + r')\\b')\n", "    def replacer(match):\n", "        abbr = match.group(0)\n", "        return abbr_map[abbr]\n", "    return pattern.sub(replacer, text)\n", "\n", "# Example usage\n", "examples = [\n", "    \"What is the MBL and ABL?\",\n", "    \"I have MBL, not just anyMBLthing.\",\n", "    \"MBL, ABL and other limits.\",\n", "    \"this isMBLnot okay but MBL is okay.\",\n", "    \"MBL.\",\n", "    \"The MBL:\",\n", "    \"ABL!\",\n", "    \"(MBL) and [ABL] are here.\",\n", "    \"What is the MBL for a Class 1 employee?; How much of <PERSON><PERSON>'s fee is covered for an AVP?\"\n", "]\n", "\n", "for q in examples:\n", "    print(expand_abbreviations(q))"]}, {"cell_type": "code", "execution_count": null, "id": "d81cb08b", "metadata": {}, "outputs": [], "source": ["import os \n", "os.path.basename(\"./page_images/page_1.png\")"]}, {"cell_type": "code", "execution_count": null, "id": "df763aed", "metadata": {}, "outputs": [], "source": ["import fitz  # PyMuPDF\n", "import os\n", "from pathlib import Path\n", "\n", "def convert_pdf_to_images(pdf_path: str, output_folder: str, dpi: int = 200):\n", "    \"\"\"\n", "    Converts each page of a PDF document into a high-resolution PNG image.\n", "\n", "    Args:\n", "        pdf_path (str): The file path of the PDF to convert.\n", "        output_folder (str): The folder where the output images will be saved.\n", "        dpi (int): The resolution (dots per inch) for the output images.\n", "                     Higher DPI means better quality and larger file size.\n", "    \"\"\"\n", "    # 1. Create the output folder if it doesn't exist\n", "    Path(output_folder).mkdir(parents=True, exist_ok=True)\n", "    \n", "    # 2. Open the PDF file\n", "    doc = fitz.open(pdf_path)\n", "    pdf_filename = Path(pdf_path).stem  # Get the PDF's name without extension\n", "\n", "    print(f\"Starting conversion of '{pdf_filename}.pdf'...\")\n", "\n", "    # 3. Iterate through each page of the PDF\n", "    for page_num, page in enumerate(doc):\n", "        # 4. Render the page to an image (pixmap)\n", "        # The DPI parameter controls the image resolution.\n", "        pix = page.get_pixmap(dpi=dpi)\n", "        \n", "        # 5. Create the output image file path\n", "        output_path = os.path.join(output_folder, f\"{pdf_filename}_page_{page_num + 1}.png\")\n", "        \n", "        # 6. Save the image as a PNG file\n", "        pix.save(output_path)\n", "        print(f\"  - Saved: {output_path}\")\n", "\n", "    doc.close()\n", "    print(\"\\nConversion complete.\")\n", "\n", "# --- How to Use ---\n", "\n", "# Make sure you have PyMuPDF installed: pip install PyMuPDF\n", "\n", "# 1. Define the path to your PDF file\n", "# pdf_file = \"data/1_MEDICARD.pdf\"\n", "# pdf_file = \"data/2_VL POLICY.pdf\"\n", "# pdf_file = \"data/Activ_Health_Platinum_Essential_Policy_Wording_ca300bebe2.pdf\"\n", "pdf_file = \"data/Care_Heart_Policy_Wording_fb66f443af.pdf\"\n", "\n", "# 2. Define the folder where you want to save the images\n", "image_output_folder = \"images\"\n", "\n", "# 3. Call the function\n", "if os.path.exists(pdf_file):\n", "    convert_pdf_to_images(pdf_file, image_output_folder)\n", "else:\n", "    print(f\"Error: PDF file not found at '{pdf_file}'\")"]}, {"cell_type": "code", "execution_count": null, "id": "2b099a63", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}