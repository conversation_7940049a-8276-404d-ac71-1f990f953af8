
import streamlit as st
import streamlit_authenticator as stauth
import yaml
from yaml.loader import SafeLoader
import os


st.set_page_config(page_title="RAG application", layout="wide")


# --- Hide sidebar if not authenticated ---
def hide_sidebar():
    hide_sidebar_style = """
        <style>
        [data-testid="stSidebar"], .stSidebar, section[data-testid="stSidebar"] {
            display: none !important;
        }
        </style>
    """
    st.markdown(hide_sidebar_style, unsafe_allow_html=True)

# --- Logo (only show if authenticated) ---
def show_logo():
    st.logo(
        "static/images/tiger-analytics.png",
        size="large",
        icon_image="static/images/tiger-analytics.png",
    )
    logo_style = """
        <style>
            .stLogo {height: 3rem !important}
        </style>
    """
    st.markdown(logo_style, unsafe_allow_html=True)


# hide header
hide_streamlit_header = """<style>
                           header {display: none !important;}
                           </style>
                        """
st.markdown(hide_streamlit_header, unsafe_allow_html=True)


stMainBlockContainerPaddingTop = """
    <style>
        .stMainBlockContainer {
            padding-top: 1rem !important;
        }
    </style>
    """
st.markdown(stMainBlockContainerPaddingTop, unsafe_allow_html=True)

CONFIG_FILE = 'config/login_config.yaml'

# Create config directory if it doesn't exist
os.makedirs(os.path.dirname(CONFIG_FILE), exist_ok=True)

# Load configuration
if os.path.exists(CONFIG_FILE):
    with open(CONFIG_FILE) as file:
        config = yaml.load(file, Loader=SafeLoader)
else:
    config = {
        'credentials': {'usernames': {}},
        'cookie': {
            'expiry_days': 30,
            'key': 'some_signature_key',
            'name': 'some_cookie_name'
        }
    }

# Initialize authenticator
authenticator = stauth.Authenticate(
    config['credentials'],
    config['cookie']['name'],
    config['cookie']['key'],
    config['cookie']['expiry_days']
)

# Initialize session state variables if they don't exist
if 'authentication_status' not in st.session_state:
    st.session_state['authentication_status'] = None
if 'show_register' not in st.session_state:
    st.session_state['show_register'] = False
if 'username' not in st.session_state:
    st.session_state['username'] = None
if 'name' not in st.session_state:
    st.session_state['name'] = None


# --- Main App Logic ---
if st.session_state['show_register']:
    # Registration Page
    hide_sidebar()
    st.title("Register")
    back_button = st.button("Back to Login")
    if back_button:
        st.session_state['show_register'] = False
        st.rerun()
    try:
        registration_result = authenticator.register_user(location='main')
        if registration_result is not None:
            email, username, name = registration_result
            if email:
                st.success('User registered successfully')
                # Save updated config
                with open(CONFIG_FILE, 'w') as file:
                    yaml.dump(config, file, default_flow_style=False)
                # Automatically switch back to login view
                st.session_state['show_register'] = False
                st.rerun()
    except Exception as e:
        st.error(e)
else:
    # Login Page or Main Content based on authentication status
    authenticator.login(location='main')

    if st.session_state.get('authentication_status'):
        # User is authenticated - show main content
        show_logo()
        nav_item_size = """
            <style>
                ul > li > div > a > span {font-size: 1.5rem !important}
            </style>
        """
        st.markdown(nav_item_size, unsafe_allow_html=True)
        # pages
        pg = st.navigation(
            [st.Page("chat.py", title="Chat"),
             st.Page("settings.py", title="Settings"),
             st.Page("langfuse_monitor.py", title="Dashboard")],
            position="sidebar",
        )
        pg.run()

        # Add user profile section to sidebar bottom
        with st.sidebar:
            # Get user information
            user_name = st.session_state.get("username", "User")
            name = st.session_state.get("name", "User")

            # Generate user initials
            def get_user_initials(full_name):
                if not full_name or full_name == "User":
                    return "U"
                words = full_name.strip().split()
                if len(words) >= 2:
                    return (words[0][0] + words[1][0]).upper()
                elif len(words) == 1:
                    return words[0][0].upper()
                return "U"

            initials = get_user_initials(name)

            # Enhanced user profile styling
            profile_style = """
                <style>
                    .sidebar-bottom-section {
                        position: fixed;
                        bottom: 80px;
                        left: 16px;
                        right: 16px;
                        width: calc(100% - 32px);
                        max-width: 280px;
                        z-index: 1000;
                    }

                    .user-profile-wrapper {
                        background: rgba(255, 255, 255, 0.95);
                        backdrop-filter: blur(10px);
                        border-radius: 16px;
                        padding: 16px;
                        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                        margin-bottom: 12px;
                    }

                    .user-profile-container {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        gap: 12px;
                    }

                    .user-info-section {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                        flex: 1;
                    }

                    .user-circle {
                        width: 45px;
                        height: 45px;
                        border-radius: 50%;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: white;
                        font-weight: 600;
                        font-size: 16px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                        border: 2px solid rgba(255, 255, 255, 0.2);
                    }

                    .user-circle:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
                        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
                    }

                    .user-details {
                        flex: 1;
                        min-width: 0;
                    }

                    .user-name {
                        font-weight: 600;
                        font-size: 14px;
                        color: #2c3e50;
                        margin: 0;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .user-username {
                        font-size: 12px;
                        color: #7f8c8d;
                        margin: 2px 0 0 0;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .company-footer {
                        background: rgba(255, 255, 255, 0.9);
                        backdrop-filter: blur(10px);
                        border-radius: 12px;
                        padding: 8px 12px;
                        text-align: center;
                        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
                        border: 1px solid rgba(255, 255, 255, 0.2);
                    }

                    .company-footer p {
                        margin: 0;
                        font-size: 11px;
                        color: #7f8c8d;
                        font-weight: 500;
                    }

                    /* Responsive adjustments */
                    @media (max-width: 768px) {
                        .sidebar-bottom-section {
                            bottom: 60px;
                            left: 8px;
                            right: 8px;
                            width: calc(100% - 16px);
                        }
                    }
                </style>
            """
            st.markdown(profile_style, unsafe_allow_html=True)

            # Create the bottom section with user profile and company footer
            st.markdown(f"""
                <div class="sidebar-bottom-section">
                    <div class="user-profile-wrapper">
                        <div class="user-profile-container">
                            <div class="user-info-section">
                                <div class="user-circle" title="User Profile">
                                    {initials}
                                </div>
                                <div class="user-details">
                                    <p class="user-name">{name}</p>
                                    <p class="user-username">@{user_name}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="company-footer">
                        <p>Powered by Tiger Analytics™</p>
                    </div>
                </div>
            """, unsafe_allow_html=True)

            # Add invisible spacer to prevent content overlap
            st.markdown("<div style='height: 160px;'></div>", unsafe_allow_html=True)

            # User profile popover (positioned at top of sidebar for better UX)
            with st.expander("👤 User Profile", expanded=False):
                st.markdown(f"**Full Name:** {name}")
                st.markdown(f"**Username:** @{user_name}")
                st.markdown("---")
                # Logout button in expander
                authenticator.logout('🚪 Logout', 'main')
    else:
        # Not authenticated (either failed or not attempted)
        hide_sidebar()
        if st.session_state.get('authentication_status') is False:
            st.error('Username/password is incorrect')
        elif st.session_state.get('authentication_status') is None:
            st.warning('Please enter your username and password')
        st.write("Don't have an account?")
        register_button = st.button("Register")
        if register_button:
            st.session_state['show_register'] = True
            st.rerun()

# Force rerun after logout to clear sidebar
if (
    'authentication_status' in st.session_state
    and st.session_state['authentication_status'] is None
    and st.session_state.get('just_logged_out') != True
):
    st.session_state['just_logged_out'] = True
    st.rerun()
else:
    st.session_state['just_logged_out'] = False