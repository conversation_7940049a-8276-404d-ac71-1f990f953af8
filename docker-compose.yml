services:
  streamlit:
    container_name: kearney_app
    build:
      dockerfile: app.Dockerfile
      context: ./
    volumes:
      - $PWD:/work
    ports:
      - 8502:8502

    deploy:
      resources:
        limits:
          memory: 8G        # Allocating 4GB of RAM
          cpus: '4'         # Allocating 2 CPU cores
        reservations:
          memory: 2G        # Reserving 2GB of RAM as a minimum
          cpus: '2'
